<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-sex" min="0" class="form-control selectpicker" name="row[merchant_id]">
                {foreach name="merchantList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.merchant_id|htmlentities"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('标签')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-label" min="0" class="form-control selectpicker" name="row[label]">
                {foreach name="labelList" item="vo"}
                <option value="{$vo}" {in name="vo" value="row.label|htmlentities"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('城市')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[city]" type="text" value="{$row.city|htmlentities}"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
            <button type="button" class="btn btn-primary" data-toggle="addresspicker" data-input-id="c-address" data-lng-id="c-lng" data-lat-id="c-lat">点击获取地址</button>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('经度')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lng" class="form-control" name="row[lng]" value="{$row.lng|htmlentities}" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('纬度')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lat" class="form-control" name="row[lat]" value="{$row.lat|htmlentities}" type="text">
        </div>
    </div>

    <div class="form-group">
        <label for="c-image" class="control-label col-xs-12 col-sm-2">{:__('门店封面')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" data-rule="" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('周一至周五营业时间')}:</label>
        <!--        <div class="col-xs-12 col-sm-8">-->
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_start_time]"
                   data-plugin="datetimepicker"
                   data-range="true" data-rule="required"
                   value="{:$row.business_hours_start_time?$row.business_hours_start_time:''}">

        </div>
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_end_time]"
                   data-plugin="datetimepicker"
                   data-range="true" data-rule="required"
                   value="{:$row.business_hours_end_time?$row.business_hours_end_time:''}">

        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('周六至周日营业时间')}:</label>
        <!--        <div class="col-xs-12 col-sm-8">-->
        <!--            <input id="c-business_hours_time2" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true" name="row[business_hours_time2]" type="text" value="{:date('Y-m-d H:i:s')}">-->
        <!--        </div>-->
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_start_time2]"
                   data-plugin="datetimepicker"
                   data-range="true" data-rule="required"
                   value="{:$row.business_hours_start_time2?$row.business_hours_start_time2:''}">

        </div>
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_end_time2]"
                   data-plugin="datetimepicker"
                   data-range="true" data-rule="required"
                   value="{:$row.business_hours_end_time2?$row.business_hours_end_time2:''}">

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('节假日营业时间')}:</label>
        <!--        <div class="col-xs-12 col-sm-8">-->
        <!--            <input id="c-business_hours_time2" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true" name="row[business_hours_time2]" type="text" value="{:date('Y-m-d H:i:s')}">-->
        <!--        </div>-->
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_start_time3]"
                   data-plugin="datetimepicker"
                   data-range="true" data-rule="required"
                   value="{:$row.business_hours_start_time3?$row.business_hours_start_time3:''}">

        </div>
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_end_time3]"
                   data-plugin="datetimepicker"
                   data-range="true" data-rule="required"
                   value="{:$row.business_hours_end_time3?$row.business_hours_end_time3:''}">

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_user_id')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-store-user-id" min="0" class="form-control selectpicker" name="row[store_user_id]">
                {foreach name="storeUser" item="vo"}
                <option value="{$key}" {in name="key" value="$row.store_user_id|htmlentities"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <!--    <div class="form-group">-->
    <!--        <label class="control-label col-xs-12 col-sm-2">{:__('Store_user_name')}:</label>-->
    <!--        <div class="col-xs-12 col-sm-8">-->
    <!--            <input id="c-store_user_name" class="form-control" name="row[store_user_name]" type="text" value="{$row.store_user_name|htmlentities}">-->
    <!--        </div>-->
    <!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" class="form-control" name="row[mobile]" type="number" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Switch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-switch" class="form-control selectpicker" name="row[switch]">
                {foreach name="statusList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.switch|htmlentities"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>

    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:$row.create_time?datetime($row.create_time):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
