<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-sex" min="0" class="form-control selectpicker" name="row[merchant_id]">
                {foreach name="merchantList" item="vo"}
                <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Start_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-start_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[start_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('End_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[end_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" class="form-control" step="0.01" name="row[money]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Use_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('use_ids[]', $storedata, null, ['class'=>'form-control selectpicker', 'multiple'=>''])}
        </div>
        <span>默认全部</span>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('store_ids[]', $storedata, null, ['class'=>'form-control selectpicker', 'multiple'=>''])}
        </div>
        <span>默认全部</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('No_project_ids')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('no_project_ids[]', $projectList, null, ['class'=>'form-control selectpicker', 'multiple'=>''])}
        </div>
        <span>默认全部</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_stacking')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-is_stacking" class="form-control selectpicker" name="row[is_stacking]">
                {foreach name="isStackingList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('优惠券类型')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-type" class="form-control selectpicker" name="row[type]">
                {foreach name="typeList" item="vo"}
                <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hair_level_scope')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('hair_level_scope[]', $hairLevelList, null, ['class'=>'form-control selectpicker', 'multiple'=>''])}
        </div>
        <span>默认全部</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
