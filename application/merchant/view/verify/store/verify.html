<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group" >
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select disabled id="c-gender" class="form-control selectpicker" name="row[gender]">
                {foreach name="genderList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.gender"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Age')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-age" class="form-control" name="row[age]" type="number" value="{$row.age|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Career')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-career" class="form-control" name="row[career]" type="text" value="{$row.career|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Permanent')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-permanent" class="form-control" name="row[permanent]" type="text" value="{$row.permanent|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:$row.create_time?datetime($row.create_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-channel" class="form-control" name="row[channel]" type="text" value="{$row.channel.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Intention_city')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input disabled id="c-intention_city" class="form-control" data-toggle="city-picker" name="row[intention_city]" type="text" value="{$row.intention_city|htmlentities}"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Budget')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-budget" class="form-control" name="row[budget]" type="text" value="{$row.budget['name']|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Invest_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-invest_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[invest_time]" type="text" value="{:$row.invest_time?datetime($row.invest_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hosting')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select disabled id="c-hosting" class="form-control selectpicker" name="row[hosting]">
                {foreach name="hostingList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.hosting"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_brand')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select disabled id="c-is_brand" class="form-control selectpicker" name="row[is_brand]">
                {foreach name="isBrandList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.is_brand"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_youjian')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select disabled id="c-is_youjian" class="form-control selectpicker" name="row[is_youjian]">
                {foreach name="isYoujianList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.is_youjian"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_hairdressing')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select disabled id="c-is_hairdressing" class="form-control selectpicker" name="row[is_hairdressing]">
                {foreach name="isHairdressingList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.is_hairdressing"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Resource')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-resource" class="form-control" name="row[resource]" type="text" value="{$row.resource.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Risk_taking')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-risk_taking" class="form-control" name="row[risk_taking]" type="text" value="{$row.taking.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fund_source')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-fund_source" class="form-control" name="row[fund_source]" type="text" value="{$row.source.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Plan')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input disabled id="c-plan" class="form-control" name="row[plan]" type="text" value="{$row.plan.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_verify')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-is_verify" class="form-control selectpicker" name="row[is_verify]">
                {foreach name="isVerifyList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.is_verify"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Verify_remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-verify_remark" class="form-control " rows="5" name="row[verify_remark]" cols="50">{$row.verify_remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
