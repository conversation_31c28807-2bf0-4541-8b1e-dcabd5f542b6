<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">规则名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" type="text" data-rule="required" class="form-control" name="row[name]" value=""  />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('计费方式')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="statusList" item="vo"}
                <label for="row[express_type]-{$key}"><input id="row[express_type]-{$key}" name="row[express_type]" type="radio" value="{$key}" {in name="key" value="2"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group" style="margin-bottom:0px">
        <label class="control-label col-xs-12 col-sm-2">{:__('运费规则')}:</label>
    </div>
    <div id="rulearea">

    </div>
    <div class="form-group kuaidi">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" href="{:url('delivery.postage/select')}"   class="btn btn-primary" id="addarea"><i class="fa fa-plus-square"></i> {:__('新增条目')}</button></span>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
