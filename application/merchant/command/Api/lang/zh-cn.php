<?php

return [
    'Info'             => '基础信息',
    'Sandbox'          => '在线测试',
    'Sampleoutput'     => '返回示例',
    'Headers'          => 'Headers',
    'Parameters'       => '参数',
    'Body'             => '正文',
    'Name'             => '名称',
    'Type'             => '类型',
    'Required'         => '必选',
    'Description'      => '描述',
    'Send'             => '提交',
    'Reset'            => '重置',
    'Tokentips'        => 'Token在会员注册或登录后都会返回,WEB端同时存在于Cookie中',
    'Apiurltips'       => 'API接口URL',
    'Savetips'         => '点击保存后Token和Api url都将保存在本地Localstorage中',
    'Authorization'    => '权限',
    'NeedLogin'        => '登录',
    'NeedRight'        => '鉴权',
    'ReturnHeaders'    => '响应头',
    'ReturnParameters' => '返回参数',
    'Response'         => '响应输出',
];
