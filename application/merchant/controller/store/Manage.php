<?php

namespace app\merchant\controller\store;

use app\common\controller\MerchantBackend;
use Exception;
use think\Db;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Session;

/**
 * 门店管理
 *
 * @icon fa fa-circle-o
 */
class Manage extends MerchantBackend
{

    /**
     * Manage模型对象
     * @var \app\model\store\Manage
     */
    protected $model = null;
    protected $storeUserModel = null;
    protected $adminModel;
    protected $groupModel;
    protected $authGroupAccessModel;
    protected $merchantModel;
    protected $labelModel;
    protected $loginList;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\model\store\Manage;
        $this->storeUserModel = new \app\model\store\User;
        $this->adminModel = new \app\merchant\model\Admin;
        $this->groupModel = new \app\merchant\model\AuthGroup;
        $this->authGroupAccessModel = new \app\merchant\model\AuthGroupAccess;
        $this->merchantModel = new \app\model\Merchant;
        $this->labelModel = new \app\model\store\Label;
        $this->loginList = LoginList('merchant');
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with('merchant')
                    ->where($where)
                    ->where('manage.merchant_id',$this->loginList['merchant_id'])
                    ->order($sort, $order)
                    ->paginate($limit);
            foreach ($list as $row) {
                $row->visible(['id','merchant_id','name','city','address','business_hours_time','store_user_name','mobile','switch','create_time','merchant']);

            }
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        $merchant = LoginList('merchant');
        if (false === $this->request->isPost()) {
            // 获取商户列表
            $merchantList = merchant_list($this->loginList['merchant_id']);
            $this->assign('merchantList',$merchantList);
            // 门店用户表
            $storeUser = $this->storeUserModel->where('status',1)->column('id,nickname');
            $this->assign('storeUser',$storeUser );
            // 获取门店标签列表
            $labelList = $this->labelModel->where('merchant_id',$this->loginList['merchant_id'])->column('id,name');
            $this->assign('labelList',$labelList );
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if( empty($params['merchant_id'])){
            $this->error(__('请先选择商户', ''));
        }
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $params['business_hours_time1'] = $params['business_hours_start_time'].'-'.$params['business_hours_end_time'];
            $params['business_hours_time2'] = $params['business_hours_start_time2'].'-'.$params['business_hours_end_time2'];
            //获取门店负责人姓名
            $params['store_user_name'] = $this->storeUserModel->where('id',$params['store_user_id'])->value('nickname');
            $result = $this->model->allowField(true)->save($params);
            if( $result ){
                // 添加门店成功后追加 门店负责人账号进入admin用户表并且赋予权限
                $store_id = $this->model->getLastInsID();
                // 查询负责人信息
                $storeUserList = $this->storeUserModel->where('id',$params['store_user_id'])->find();
                // 查询负责人账号是否存在admin 中   已存在不添加新的权限组
                $is_username = $this->adminModel->where('username',$storeUserList['username'])->find();
                if( empty( $is_username ) ){
                    $adminData = [
                        'username'      => $storeUserList['username'],
                        'nickname'      => $storeUserList['nickname'],
                        'password'      => md5(md5($storeUserList['password']) . $storeUserList['salt']),
                        'salt'          => $storeUserList['salt'],
                        'avatar'        => $storeUserList['avatar_image'],
                        'email'         => $storeUserList['email'],
                        'mobile'        => $storeUserList['mobile'],
                        'merchant_id'   => $params['merchant_id'],
                        'store_id'      => $store_id,
                        'platform'      => 2
                    ];
                    // 同步插入admin表
                    $this->adminModel->save( $adminData );
                    // 同步插入auth_group 一条默认权限组数据  平台为商户
                    $groupData = [
                        'pid' => 0,
                        'name' => 'Admin group',
                        'rules' => '*',
                        'createtime' => time(),
                        'status' => 'normal',
                        'merchant_id'   => $params['merchant_id'],
                        'store_id'   => $store_id,
                        'platform' => 2
                    ];
                    $this->groupModel->save( $groupData );
                    // 插入权限组一条数据
                    $dataset = ['uid' => $this->adminModel->id, 'group_id' => $this->groupModel->id];
                    $this->authGroupAccessModel->save($dataset);
                }

            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            // 获取商户列表
            $merchantList = merchant_list($this->loginList['merchant_id']);
            $this->assign('merchantList',$merchantList);
            // 门店用户表
            $storeUser = $this->storeUserModel->where('status',1)->column('id,nickname');
            $this->assign('storeUser',$storeUser );
            // 获取门店标签列表
            $labelList = $this->labelModel->where('merchant_id',$this->loginList['merchant_id'])->column('id,name');
            $this->assign('labelList',$labelList );
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if( empty($params['merchant_id'])){
            $this->error(__('请先选择商户', ''));
        }
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $provinceList = explode('/',$params['city']);
            $params['province'] = $provinceList[0];
            $params['city1'] = $provinceList[1];
            $params['area'] = $provinceList[2];
            $params['business_hours_time1'] = $params['business_hours_start_time'].'-'.$params['business_hours_end_time'];
            $params['business_hours_time2'] = $params['business_hours_start_time2'].'-'.$params['business_hours_end_time2'];
            $params['business_hours_time3'] = $params['business_hours_start_time3'].'-'.$params['business_hours_end_time3'];
            //获取门店负责人姓名
            $params['store_user_name'] = $this->storeUserModel->where('id',$params['store_user_id'])->value('nickname');
            $result = $row->allowField(true)->save($params);
            if( $result ){
                // 添加门店成功后追加 门店负责人账号进入admin用户表并且赋予权限
                $store_id = $this->model->getLastInsID();
                // 查询负责人信息
                $storeUserList = $this->storeUserModel->where('id',$params['store_user_id'])->find();
                // 查询负责人账号是否存在admin 中   已存在不添加新的权限组
                $is_username = $this->adminModel->where('username',$storeUserList['username'])->find();
                if( empty( $is_username ) ){
                    $adminData = [
                        'username'      => $storeUserList['username'],
                        'nickname'      => $storeUserList['nickname'],
                        'password'      => md5(md5($storeUserList['password']) . $storeUserList['salt']),
                        'salt'          => $storeUserList['salt'],
                        'avatar'        => $storeUserList['avatar_image'],
                        'email'         => $storeUserList['email'],
                        'mobile'        => $storeUserList['mobile'],
                        'merchant_id'   => $params['merchant_id'],
                        'store_id'      => $store_id,
                        'platform'      => 2
                    ];
                    // 同步插入admin表
                    $this->adminModel->save( $adminData );
                    // 同步插入auth_group 一条默认权限组数据  平台为商户
                    $groupData = [
                        'pid' => 0,
                        'name' => 'Admin group',
                        'rules' => '*',
                        'createtime' => time(),
                        'status' => 'normal',
                        'merchant_id'   => $params['merchant_id'],
                        'store_id'   => $store_id,
                        'platform' => 2
                    ];
                    $this->groupModel->save( $groupData );
                    // 插入权限组一条数据
                    $dataset = ['uid' => $this->adminModel->id, 'group_id' => $this->groupModel->id];
                    $this->authGroupAccessModel->save($dataset);
                }

            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }
}
