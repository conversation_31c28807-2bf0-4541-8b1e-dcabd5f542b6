<?php

namespace app\merchant\controller\delivery;

use app\common\controller\MerchantBackend;
use fast\Tree;
use think\Collection;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 发货订单管理
 *
 * @icon fa fa-circle-o
 */
class Cat extends MerchantBackend
{

    /**
     * Subjects模型对象
     * @var \app\addons\delivery\model\Cat
     */
    protected $model = null;
    protected $merchantModel = null;
    protected $loginList = null;
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model =model('addons\delivery\model\Cat');
        $this->merchantModel = new \app\model\Merchant;
        // 获取登陆信息
        $this->loginList = LoginList('merchant');
    }


    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

//            $list = $this->model
//                ->where($where)
//                ->field(true)
//                ->order($sort, $order)
//                ->paginate($limit);
            // 必须将结果集转换为数组
            $list = \think\Db::name("delivery_cat")->field('id,pid,name,create_time')
                ->where('merchant_id',$this->loginList['merchant_id'])
                ->order('id ASC')->select();
            Tree::instance()->init($list)->icon = ['&nbsp;&nbsp;&nbsp;&nbsp;', '&nbsp;&nbsp;&nbsp;&nbsp;', '&nbsp;&nbsp;&nbsp;&nbsp;'];
            $list = Tree::instance()->getTreeList(Tree::instance()->getTreeArray(0), 'name');

            $result = array("total" => count($list), "rows" => $list);
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        // 获取登陆信息
        $LoginList = LoginList('merchant');
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                //$params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }

                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $params['merchant_id'] = $LoginList['merchant_id'];
                    $params['create_time'] = time();
                    $result = $this->model->allowField(true)->save($params);

                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $cat_list = $this->model->where([
            'merchant_id' => $LoginList['merchant_id'],
            'pid' => 0
        ])->column('id,name');
        $this->assign('catList',$cat_list);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        // 获取登陆信息
        $LoginList = LoginList('merchant');
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                //$params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $params['merchant_id'] = $LoginList['merchant_id'];
                    $result = $row->allowField(true)->save($params);

                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 获取父级
        if( $row->pid > 0 ){
            $cat_list = $this->model->where([
                'merchant_id' => $LoginList['merchant_id'],
                'id' => $row->pid
            ])->column('id,name');
        }else{
            $cat_list = $this->model->where([
                'merchant_id' => $LoginList['merchant_id'],
                'pid' => 0
            ])->column('id,name');
        }
        $this->assign('catList',$cat_list);

        $this->assign('row',$row);
        return $this->view->fetch();
    }

}
