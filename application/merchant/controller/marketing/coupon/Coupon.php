<?php

namespace app\merchant\controller\marketing\coupon;

use app\common\controller\MerchantBackend;
use app\merchant\model\AuthGroup;
use Exception;
use fast\Tree;
use think\Db;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Session;

/**
 * 优惠券
 *
 * @icon fa fa-circle-o
 */
class Coupon extends MerchantBackend
{

    /**
     * Coupon模型对象
     * @var \app\model\marketing\coupon\Coupon
     */
    protected $model = null;
    protected $storeModel = null;
    protected $projectModel = null;
    protected $hairLevelModel = null;
    protected $loginList = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\model\marketing\coupon\Coupon;
        $this->storeModel = new \app\model\store\Manage;
        $this->projectModel = new \app\model\Project;
        $this->hairLevelModel = new \app\model\data\hair\Level;
        $this->loginList = LoginList('merchant');
        $this->view->assign("isStackingList", $this->model->getIsStackingList());
        $this->view->assign("typeList", $this->model->getTypeList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['merchant'])
                    ->where($where)
                    ->where('coupon.merchant_id',$this->loginList['merchant_id'])
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','start_time','end_time','money','is_stacking','type']);
                $row->visible(['merchant']);
				$row->getRelation('merchant')->visible(['merchant_name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {

            // 获取商户列表
            $merchantList = merchant_list($this->loginList['merchant_id']);
            $this->view->assign('merchantList', $merchantList);
            // 获取门店列表
            $storeList = $this->storeModel->where('switch', '=', 1)->column('id,name');
            $this->view->assign('storedata', $storeList);
            // 获取项目列表
            $projectList = $this->projectModel->where('merchant_id', '=', $this->loginList['merchant_id'])->column('id,name');
            $this->view->assign('projectList', $projectList);
            // 获取发型师等级列表
            $hairLevelList = $this->hairLevelModel->column('value,name');
            $this->view->assign('hairLevelList', $hairLevelList);

            return $this->view->fetch();
        }
        $post = $this->request->post();
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            if(!empty($post['use_ids'])) $params['use_ids'] = implode(',',$post['use_ids']);else $params['use_ids'] = '';
            if(!empty($post['store_ids'])) $params['store_ids'] = implode(',',$post['store_ids']);else $params['store_ids'] = '';
            if(!empty($post['no_project_ids'])) $params['no_project_ids'] = implode(',',$post['no_project_ids']);else $params['no_project_ids'] = '';
            if(!empty($post['hair_level_scope'])) $params['hair_level_scope'] = implode(',',$post['hair_level_scope']);else $params['hair_level_scope'] = '';

            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            // 获取商户列表
            $merchantList = merchant_list($this->loginList['merchant_id']);
            $this->view->assign('merchantList', $merchantList);
            // 获取门店列表
            $storeList = $this->storeModel->where('switch', '=', 1)->column('id,name');
            $this->view->assign('storedata', $storeList);
            // 获取项目列表
            $projectList = $this->projectModel->where('merchant_id', '=', $this->loginList['merchant_id'])->column('id,name');
            $this->view->assign('projectList', $projectList);
            // 获取发型师等级列表
            $hairLevelList = $this->hairLevelModel->column('value,name');
            $this->view->assign('hairLevelList', $hairLevelList);

            if(!empty($row->use_ids)) $use_ids = explode(',',$row->use_ids);else $use_ids = null;
            if(!empty($row->store_ids)) $store_ids = explode(',',$row->store_ids);else $store_ids = null;
            if(!empty($row->no_project_ids)) $no_project_ids = explode(',',$row->no_project_ids);else $no_project_ids = null;
            if(!empty($row->hair_level_scope)) $hair_level_scope = explode(',',$row->hair_level_scope);else $hair_level_scope = null;

            $this->view->assign("row", $row);
            $this->view->assign("use_ids", $use_ids);
            $this->view->assign("store_ids", $store_ids);
            $this->view->assign("no_project_ids", $no_project_ids);
            $this->view->assign("hair_level_scope", $hair_level_scope);
            return $this->view->fetch();
        }
        $post = $this->request->post();
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }

            if(!empty($post['use_ids'])) $params['use_ids'] = implode(',',$post['use_ids']);else $params['use_ids'] = '';
            if(!empty($post['store_ids'])) $params['store_ids'] = implode(',',$post['store_ids']);else $params['store_ids'] = '';
            if(!empty($post['no_project_ids'])) $params['no_project_ids'] = implode(',',$post['no_project_ids']);else $params['no_project_ids'] = '';
            if(!empty($post['hair_level_scope'])) $params['hair_level_scope'] = implode(',',$post['hair_level_scope']);else $params['hair_level_scope'] = '';

            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

}
