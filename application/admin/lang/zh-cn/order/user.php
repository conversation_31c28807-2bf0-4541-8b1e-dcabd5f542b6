<?php

return [
    'Id'                     => '主键ID',
    'Merchant_id'            => '商户ID',
    'User_id'                => '用户ID',
    'Order_no'               => '订单号',
    'Money'                  => '金额',
    'Pay_time'               => '支付时间',
    'Pay_status'             => '支付状态(单选)',
    'Pay_status 1'           => '待支付',
    'Pay_status 2'           => '已支付',
    'Pay_status 3'           => '取消支付',
    'Status'                 => '订单状态(单选)',
    'Status 1'               => '待付款',
    'Set status to 1'        => '设为待付款',
    'Status 2'               => '已付款',
    'Set status to 2'        => '设为已付款',
    'Merchant.merchant_name' => '商户名称',
    'User.nickname'          => '昵称'
];
