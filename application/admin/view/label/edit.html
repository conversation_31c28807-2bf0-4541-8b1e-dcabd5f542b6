<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-merchant_id" min="0" class="form-control selectpicker" name="row[merchant_id]">
                {foreach name="merchantList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.merchant_id"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('标签类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-type" class="form-control selectpicker" name="row[type]">
                <option value="1" {in name="1" value="$row.type|htmlentities"}selected{/in}>商品</option>
                <option value="2" {in name="2" value="$row.type|htmlentities"}"}selected{/in}>理发</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:$row.create_time?datetime($row.create_time):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

