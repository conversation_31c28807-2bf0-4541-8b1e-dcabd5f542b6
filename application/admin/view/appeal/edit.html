<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-merchant_id" data-rule="required" data-source="merchant/index" class="form-control selectpage" name="row[merchant_id]" type="text" value="{$row.merchant_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/manage/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hair_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hair_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[hair_id]" type="text" value="{$row.hair_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Record_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-record_id" data-rule="required" data-source="hair/record/index" class="form-control selectpage" name="row[record_id]" type="text" value="{$row.record_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Appeal_content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-appeal_content" class="form-control editor" rows="5" name="row[appeal_content]" cols="50">{$row.appeal_content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Appeal_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-appeal_image" class="form-control" size="50" name="row[appeal_image]" type="text" value="{$row.appeal_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-appeal_image" class="btn btn-danger faupload" data-input-id="c-appeal_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-appeal_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-appeal_image" class="btn btn-primary fachoose" data-input-id="c-appeal_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-appeal_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-appeal_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_verify')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_verify" class="form-control selectpicker" name="row[is_verify]">
                {foreach name="isVerifyList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.is_verify"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Appeal_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-appeal_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[appeal_time]" type="text" value="{:$row.appeal_time?datetime($row.appeal_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Verify_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-verify_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[verify_time]" type="text" value="{:$row.verify_time?datetime($row.verify_time):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
