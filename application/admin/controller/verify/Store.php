<?php

namespace app\admin\controller\verify;

use app\common\controller\Backend;

/**
 * 门店加盟申请管理
 *
 * @icon fa fa-circle-o
 */
class Store extends Backend
{

    /**
     * Store模型对象
     * @var \app\model\verify\Store
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\model\verify\Store;
        $this->view->assign("hostingList", $this->model->getHostingList());
        $this->view->assign("isBrandList", $this->model->getIsBrandList());
        $this->view->assign("isYoujianList", $this->model->getIsYoujianList());
        $this->view->assign("isHairdressingList", $this->model->getIsHairdressingList());
        $this->view->assign("genderList", $this->model->getGenderList());
        $this->view->assign("isVerifyList", $this->model->getIsVerifyList());
        $this->view->assign("progressList", $this->model->getProgressList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['budget','channel','source','plan','resource','taking'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','progress','intention_city','invest_time','hosting','is_brand','is_youjian','is_hairdressing','name','gender','age','career','permanent','create_time','is_verify']);
                $row->visible(['budget']);
				$row->getRelation('budget')->visible(['name']);
				$row->visible(['channel']);
				$row->getRelation('channel')->visible(['name']);
				$row->visible(['source']);
				$row->getRelation('source')->visible(['name']);
				$row->visible(['plan']);
				$row->getRelation('plan')->visible(['name']);
				$row->visible(['resource']);
				$row->getRelation('resource')->visible(['name']);
				$row->visible(['taking']);
				$row->getRelation('taking')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    // 审核列表
    public function verify($ids = null ){
        $id = $this->request->param('id');
        if (false === $this->request->isPost()) {

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            // 获取审核信息详情
            $list = $this->model
                ->with(['budget','channel','source','plan','resource','taking'])
                ->where($where)->find()->toArray();
            $this->assign('row',$list );
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);
        $params['verify_time'] = time();
        $res = $this->model->where('id',$id)->update( $params );
        if( $res ) $this->success('操作成功',url('verify/store'));else $this->error('操作失败',url('verify/store'));
    }

}
