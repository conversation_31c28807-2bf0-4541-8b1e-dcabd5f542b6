<?php

namespace app\storeapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\admin\model\User as userModel;
use app\model\Project as projectModel;

/**
 * 示例接口
 */
class Hair extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    protected $storeModel = null;
    protected $hairModel = null;
    protected $hairLevelModel = null;
    protected $orderHairdressingModel = null;
    protected $userModel = null;
    protected $projectModel = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->storeModel = new \app\model\store\Manage;
        $this->hairModel = new \app\model\verify\Hair;
        $this->orderHairdressingModel = new \app\model\order\Hairdressing;
        $this->hairLevelModel = new \app\model\HairLevel;
        $this->userModel = new userModel();
        $this->projectModel = new projectModel();
    }

    /**
     * 发型师列表
     *
     */
    public function getHairList()
    {
        $merchant_id = $this->request->post('merchant_id');
        $store_id = $this->request->post('store_id');
        if( !$merchant_id ){
            apijson(1,[],'缺少商户id');
        }
        if( !$store_id ){
            apijson(1,[],'缺少门店id');
        }
        try {
            $list = $this->hairModel->with(['user'])->where([
                'hair.merchant_id' => $merchant_id,
                'hair.store_id' => $store_id,
            ])->limit($this->page,$this->limit)->select();
            apijson(0,$list);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }

    }

    /**
     * 发型师等级
     */
    /**
     * @return array
     */
    public function getLevelList()
    {
        try {
            $list = $this->hairLevelModel->field('value,name')->select();
            apijson(0,$list);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }
    }
    /**
     * 新增员工调用--发型师等级
     */
    /**
     * @return array
     */
    public function getHairLevelList()
    {
        try {
            $list = $this->hairLevelModel->field('value,name')->select();
            $data = [];
            foreach ( $list as $key => $item ){
                $list[$key]['id'] = $item['value'];
                $list[$key]['label'] = $item['name'];
                unset( $list[$key]['name'] );
                unset( $list[$key]['value'] );
            }
            $data = [$list];
            apijson(0,$data);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }
    }
    /**
     * 修改发型师等级
     */
    public function editLevel(){
        $hair_id = $this->request->post('hair_id');
        $value = $this->request->post('value');
        if( !$hair_id ){
            apijson(1,'缺少发型师ID');
        }
        try {
            // 修改发型师等级 
            $res = $this->hairModel->where('id',$hair_id)->update([
                'level' => $value
            ]);
            apijson(0,'操作成功');
        } catch (UploadException $e){
            $this->error($e->getMessage());
        }
    }




    /**
     * 给门店指派员工
     * @ApiTitle    (给门店指派员工)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=true, description="门店ID")
     * @ApiParams   (name="user_ids", type="array", required=true, description="用户ID数组")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function assignStaffToStore()
    {
        $merchant_id = $this->request->post('merchant_id');
        $store_id = $this->request->post('store_id');
        $user_ids = $this->request->post('user_ids')?explode(',',$this->request->post('user_ids')):[];

        // 参数验证
        if (!$merchant_id) {
            apijson(1, [], '缺少商户ID');
        }
        if (!$store_id) {
            apijson(1, [], '缺少门店ID');
        }
        if (empty($user_ids)) {
            apijson(1, [], '请选择要指派的员工');
        }

        // 验证门店是否存在
        $store = $this->storeModel->where([
            'id' => $store_id,
            'merchant_id' => $merchant_id
        ])->find();

        if (!$store) {
            apijson(1, [], '门店不存在');
        }

        // 开始事务
        \think\Db::startTrans();

        try {
            $successCount = 0;
            $failCount = 0;
            $existCount = 0;
            $failIds = [];
            foreach ($user_ids as $user_id) {
                // 获取用户手机号
                $mobile = $this->hairModel->where('id',$user_id)->value('mobile');
                // 检查用户是否存在
                $user = $this->userModel->where('mobile', $mobile)->find();
                if (!$user) {
                    $failCount++;
                    $failIds[] = $user_id;
                    continue;
                }

                // 检查是否已经是该门店的员工
                $exists = $this->hairModel->where([
                    'merchant_id' => $merchant_id,
                    'store_id' => $store_id,
                    'user_id' => $user['id']
                ])->find();

                if ($exists) {
                    $existCount++;
                    continue;
                }

                // 添加员工到门店
                $result = $this->hairModel->where([
                    'id' => $user_id,
                    'merchant_id' => $merchant_id
                ])->update([
                    'store_id' => $store_id,
                    'user_id' => $user['id']
                ]);

                if ($result) {
                    $successCount++;
                    // 更新用户表，标记为发型师
                    $this->userModel->where('id', $user['id'])->update([
                        'is_hair' => 1,
                        'store_id' => $store_id,
                        'update_time' => time()
                    ]);
                } else {
                    $failCount++;
                    $failIds[] = $user_id;
                }
            }

            // 提交事务
            \think\Db::commit();

            $responseData = [
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'exist_count' => $existCount,
                'fail_ids' => $failIds
            ];

            apijson(0, $responseData, '指派员工成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '指派员工失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加员工
     * @ApiTitle    (添加员工)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=true, description="门店ID")
     * @ApiParams   (name="name", type="string", required=true, description="员工姓名")
     * @ApiParams   (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams   (name="level", type="integer", required=false, description="等级")
     * @ApiParams   (name="birthday", type="string", required=false, description="生日")
     * @ApiParams   (name="haircut_duration", type="integer", required=false, description="理发时长(分钟)")
     * @ApiParams   (name="intention_city", type="string", required=false, description="意向城市")
     * @ApiParams   (name="is_training", type="integer", required=false, description="是否培训中")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function addStaff()
    {
        $params = $this->request->post();
        // 必要参数验证
        $requiredFields = ['merchant_id', 'store_id', 'name', 'mobile'];
        foreach ($requiredFields as $field) {
            if (empty($params[$field])) {
                apijson(1, [], "缺少必要参数: {$field}");
            }
        }

        // 验证手机号格式
        if (!preg_match("/^1[3-9]\d{9}$/", $params['mobile'])) {
            apijson(1, [], '手机号格式不正确');
        }

        // 验证门店是否存在
        $store = $this->storeModel->where([
            'id' => $params['store_id'],
            'merchant_id' => $params['merchant_id']
        ])->find();

        if (!$store) {
            apijson(1, [], '门店不存在');
        }

        // 检查手机号是否已存在
        $exists = $this->hairModel->where('mobile', $params['mobile'])->find();
        if ($exists) {
            apijson(1, [], '该手机号已存在');
        }

        // 开始事务
        \think\Db::startTrans();

        try {
            // 检查用户是否存在，不存在则创建
            $user = $this->userModel->where('mobile', $params['mobile'])->find();

            if (!$user) {
                // 创建用户
                $userData = [
                    'username' => $params['stage_name'],
                    'nickname' => $params['name'],
                    'mobile' => $params['mobile'],
                    'password' => md5('123456'), // 默认密码
                    'is_hair' => 1, // 标记为发型师
                    'createtime' => time(),
                    'status' => 'normal'
                ];

                $this->userModel->insert($userData);
                $user_id = $this->userModel->getLastInsID();
            } else {
                $user_id = $user['id'];

                // 更新用户信息
                $this->userModel->where('id', $user_id)->update([
                    'nickname' => $params['name'],
                    'is_hair' => 1,
                    'update_time' => time()
                ]);
            }

            // 添加发型师信息
            $hairData = [
                'user_id' => $user_id,
                'merchant_id' => $params['merchant_id'],
                'store_id' => $params['store_id'],
                'name' => $params['name'],
                'stage_name' => $params['stage_name'],
                'mobile' => $params['mobile'],
                'level' => $params['level'] ?? 1,// 级别
                'birthday' => $params['birthday'] ?? '',// 出生日期
                'haircut_duration' => $params['haircut_duration'] ?? 0,// 剪发工作时长(年)
                'intention_city' => $params['intention_city'] ?? '',// 意向城市
                'is_training' => $params['is_training'] ?? 2,// 默认无培训经历
                'badabababa' => $params['badabababa'] ?? 0,// 洗剪吹单价(元)
                'field_area' => $params['field_area'] ?? '',// 擅长领域
                'background' => $params['background'] ?? '',// 背景
                'person_image' => $params['person_image'] ?? '',// 个人形象照
                'qualifications_image' => $params['qualifications_image'] ?? '',// 上传资质
                'health_certificate_image' => $params['health_certificate_image'] ?? '',// 健康证
                'id_card_image_front' => $params['id_card_image_front'] ?? '',// 身份证  正反面
                'id_card_image_side' => $params['id_card_image_side'] ?? '',// 身份证  正反面
                'is_verify' => 2,
                'switch' => 1,
                'create_time' => time()
            ];

            $result = $this->hairModel->insert($hairData);

            if (!$result) {
                apijson(1, [], '添加员工失败');
            }

            // 提交事务
            \think\Db::commit();

            apijson(0, ['id' => $this->hairModel->getLastInsID()], '添加员工成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '添加员工失败: ' . $e->getMessage());
        }
    }

    /**
     * 编辑员工
     * @ApiTitle    (编辑员工)
     * @ApiMethod   (POST)
     * @ApiParams   (name="id", type="integer", required=true, description="员工ID")
     * @ApiParams   (name="name", type="string", required=false, description="员工姓名")
     * @ApiParams   (name="mobile", type="string", required=false, description="手机号")
     * @ApiParams   (name="level", type="integer", required=false, description="等级")
     * @ApiParams   (name="birthday", type="string", required=false, description="生日")
     * @ApiParams   (name="haircut_duration", type="integer", required=false, description="理发时长(分钟)")
     * @ApiParams   (name="intention_city", type="string", required=false, description="意向城市")
     * @ApiParams   (name="is_training", type="integer", required=false, description="是否培训中")
     * @ApiParams   (name="switch", type="integer", required=false, description="是否启用")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function editStaff()
    {
        $params = $this->request->post();

        // 必要参数验证
        if (empty($params['hair_id'])) {
            apijson(1, [], '缺少员工ID');
        }
        // 查询员工信息
        $hair = $this->hairModel->where('id', $params['hair_id'])->find();
        if (!$hair) {
            apijson(1, [], '员工不存在');
        }

        // 验证手机号格式
        if (!empty($params['mobile']) && !preg_match("/^1[3-9]\d{9}$/", $params['mobile'])) {
            apijson(1, [], '手机号格式不正确');
        }

        // 检查手机号是否已存在（排除自己）
        if (!empty($params['mobile']) && $params['mobile'] != $hair['mobile']) {
            $exists = $this->hairModel->where('mobile', $params['mobile'])->where('id', '<>', $params['id'])->find();
            if ($exists) {
                apijson(1, [], '该手机号已存在');
            }
        }

        // 开始事务
        \think\Db::startTrans();

        try {
            // 更新数据
            $updateData = [];

            // 只更新提交的字段

            $allowFields = ['name', 'mobile', 'level', 'birthday', 'haircut_duration', 'intention_city', 'badabababa', 'is_training','field_area', 'background', 'person_image', 'qualifications_image', 'health_certificate_image', 'id_card_image'];
            foreach ($allowFields as $field) {
                if (isset($params[$field])) {
                    $updateData[$field] = $params[$field];
                }
            }

            if (!empty($updateData)) {
                $updateData['update_time'] = time();

                $result = $this->hairModel->where('id', $params['hair_id'])->update($updateData);

                // 如果更新了手机号或姓名，同步更新用户表
                if (isset($updateData['mobile']) || isset($updateData['name'])) {
                    $userUpdateData = [];

                    if (isset($updateData['mobile'])) {
                        $userUpdateData['mobile'] = $updateData['mobile'];
                        $userUpdateData['username'] = $updateData['mobile'];
                    }

                    if (isset($updateData['name'])) {
                        $userUpdateData['nickname'] = $updateData['name'];
                    }

                    if (!empty($userUpdateData)) {
                        $userUpdateData['update_time'] = time();
                        $this->userModel->where('id', $hair['user_id'])->update($userUpdateData);
                    }
                }
            }

            // 提交事务
            \think\Db::commit();

            apijson(0, [], '编辑员工成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '编辑员工失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取员工详情
     * @ApiTitle    (获取员工详情)
     * @ApiMethod   (POST)
     * @ApiParams   (name="id", type="integer", required=true, description="员工ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function getStaffDetail()
    {
        $id = $this->request->post('id');

        if (empty($id)) {
            apijson(1, [], '缺少员工ID');
        }

        try {
            // 查询员工信息
            $hair = $this->hairModel->where('id', $id)->find();

            if (!$hair) {
                apijson(1, [], '员工不存在');
            }

            // 获取员工等级名称
            $levelInfo = $this->hairLevelModel->where('value', $hair['level'])->find();
            $hair['level_name'] = $levelInfo ? $levelInfo['name'] : '';

            // 获取门店信息
            $store = $this->storeModel->where('id', $hair['store_id'])->find();
            $hair['store_name'] = $store ? $store['name'] : '';

            // 格式化时间
            $hair['create_time_text'] = date('Y-m-d H:i:s', $hair['create_time']);
            $hair['update_time_text'] = date('Y-m-d H:i:s', $hair['update_time']);

            // 获取员工的订单统计
//            $orderStats = $this->getStaffOrderStats($id);
//            $hair['order_stats'] = $orderStats;

            apijson(0, $hair, '获取成功');
        } catch (UploadException $e) {
            apijson(1, [], '获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取员工订单统计
     * @param int $hairId 员工ID
     * @return array 统计数据
     */
    private function getStaffOrderStats($hairId)
    {
        // 获取总订单数
        $totalOrders = $this->orderHairdressingModel->where('hair_id', $hairId)->count();

        // 获取今日订单数
        $todayStart = strtotime(date('Y-m-d'));
        $todayEnd = $todayStart + 86399;
        $todayOrders = $this->orderHairdressingModel->where([
            'hair_id' => $hairId,
            'create_time' => ['between', [$todayStart, $todayEnd]]
        ])->count();

        // 获取本月订单数
        $monthStart = strtotime(date('Y-m-01'));
        $monthEnd = strtotime(date('Y-m-t')) + 86399;
        $monthOrders = $this->orderHairdressingModel->where([
            'hair_id' => $hairId,
            'create_time' => ['between', [$monthStart, $monthEnd]]
        ])->count();

        // 获取总营业额
        $totalAmount = $this->orderHairdressingModel->where([
            'hair_id' => $hairId,
            'pay_status' => 1 // 已支付
        ])->sum('price');

        // 获取本月营业额
        $monthAmount = $this->orderHairdressingModel->where([
            'hair_id' => $hairId,
            'pay_status' => 1,
            'create_time' => ['between', [$monthStart, $monthEnd]]
        ])->sum('price');

        return [
            'total_orders' => $totalOrders,
            'today_orders' => $todayOrders,
            'month_orders' => $monthOrders,
            'total_amount' => $totalAmount,
            'month_amount' => $monthAmount
        ];
    }

    /**
     * 搜索员工
     * @ApiTitle    (搜索员工)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=false, description="门店ID")
     * @ApiParams   (name="keyword", type="string", required=false, description="搜索关键词(姓名或手机号)")
     * @ApiParams   (name="level", type="integer", required=false, description="等级")
     * @ApiParams   (name="switch", type="integer", required=false, description="状态：0=禁用，1=启用")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function searchStaff()
    {
        $params = $this->request->post();

        if (empty($params['merchant_id'])) {
            apijson(1, [], '缺少商户ID');
        }

        try {
            $where = ['merchant_id' => $params['merchant_id']];
            $where['store_id'] = $params['store_id'];

            // 添加关键词搜索条件
            if (!empty($params['keyword'])) {
                $keyword = $params['keyword'];
                $where['name|mobile'] = ['like', "%{$keyword}%"];
            }

            // 查询员工列表
            $list = $this->hairModel
                ->where($where)
                ->order('create_time DESC')
                ->limit($this->page, $this->limit)
                ->select();

            // 获取总数
            $total = $this->hairModel->where($where)->count();

            // 处理员工数据
            foreach ($list as &$item) {
                // 获取员工等级名称
                $levelInfo = $this->hairLevelModel->where('value', $item['level'])->find();
                $item['level_name'] = $levelInfo ? $levelInfo['name'] : '';

                // 获取门店名称
                $store = $this->storeModel->where('id', $item['store_id'])->find();
                $item['store_name'] = $store ? $store['name'] : '';

                // 格式化时间
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time_text'] = date('Y-m-d H:i:s', $item['update_time']);

                // 格式化状态
                $item['switch_text'] = $item['switch'] == 1 ? '启用' : '禁用';
                $item['is_training_text'] = $item['is_training'] == 1 ? '是' : '否';
            }

            $result = [
                'total' => $total,
                'list' => $list
            ];

            apijson(0, $result, '获取成功');
        } catch (UploadException $e) {
            apijson(1, [], '获取失败: ' . $e->getMessage());
        }
    }


    /**
     * 员工提成数据
     * @ApiTitle    (员工提成数据)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=true, description="门店ID")
     * @ApiParams   (name="hair_id", type="integer", required=true, description="发型师ID")
     * @ApiParams   (name="time_range", type="string", required=false, description="时间范围：month=当月,last_month=上月,half_year=近半年,all=全部")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function commissionList()
    {
        $merchant_id = $this->request->post('merchant_id');
        $store_id = $this->request->post('store_id');
        $hair_id = $this->request->post('hair_id');
        $time_range = $this->request->post('time_range', 'all');

        // 参数验证
        if (!$merchant_id) {
            apijson(1, [], '缺少商户ID');
        }
        if (!$store_id) {
            apijson(1, [], '缺少门店ID');
        }
        if (!$hair_id) {
            apijson(1, [], '缺少发型师ID');
        }

        try {
            // 构建查询条件
            $where = [
                'share_record.merchant_id' => $merchant_id,
                'share_record.store_id' => $store_id,
                'share_record.hair_id' => $hair_id
            ];

            // 根据时间范围筛选
            switch ($time_range) {
                case 'month': // 当月
                    $month_start = strtotime(date('Y-m-01'));
                    $month_end = strtotime(date('Y-m-t')) + 86399;
                    $where['share_record.create_time'] = ['between', [$month_start, $month_end]];
                    break;
                case 'last_month': // 上月
                    $last_month_start = strtotime(date('Y-m-01', strtotime('-1 month')));
                    $last_month_end = strtotime(date('Y-m-t', strtotime('-1 month'))) + 86399;
                    $where['share_record.create_time'] = ['between', [$last_month_start, $last_month_end]];
                    break;
                case 'half_year': // 近半年
                    $half_year_start = strtotime('-6 month');
                    $where['share_record.create_time'] = ['>=', $half_year_start];
                    break;
                case 'all': // 全部
                default:
                    // 不添加时间筛选
                    break;
            }
            // 查询提成记录
            $shareRecordModel = new \app\model\finance\ShareRecord();
            $list = $shareRecordModel
                ->with(['order', 'user'])
                ->where($where)
                ->order('create_time DESC')
                ->limit($this->page, $this->limit)
                ->select();

            // 计算提成总额
            $total_commission = $shareRecordModel->alias('share_record')->where($where)->sum('share_money');
            // 处理返回数据
            foreach ($list as &$item) {
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['project'] = $this->projectModel->where('id',$item['order']['project_id'])->find();
//                // 只保留需要的字段
//                $item = [
//                    'id' => $item['id'],
//                    'order_id' => $item['order_id'],
//                    'money' => $item['money'], // 订单金额
//                    'share_money' => $item['share_money'], // 提成金额
//                    'create_time' => $item['create_time'],
//                    'create_time_text' => $item['create_time_text'],
//                    'user' => isset($item['user']) ? [
//                        'nickname' => $item['user']['nickname']
//                    ] : null,
//                    'order' => isset($item['order']) ? [
//                        'order_no' => $item['order']['order_no'],
//                        'project_type' => $item['order']['project_type']
//                    ] : null
//                ];
            }

            $result = [
                'list' => $list,
                'total_commission' => $total_commission,
                'time_range' => $time_range
            ];

            apijson(0, $result, '获取成功');
        } catch (UploadException $e) {
            apijson(1, [], '获取提成记录失败: ' . $e->getMessage());
        }
    }



    /**
     * 停用员工
     * @ApiTitle    (停用员工)
     * @ApiMethod   (POST)
     * @ApiParams   (name="id", type="integer", required=true, description="员工ID")
     * @ApiParams   (name="name", type="string", required=false, description="员工姓名")
     * @ApiParams   (name="mobile", type="string", required=false, description="手机号")
     * @ApiParams   (name="level", type="integer", required=false, description="等级")
     * @ApiParams   (name="birthday", type="string", required=false, description="生日")
     * @ApiParams   (name="haircut_duration", type="integer", required=false, description="理发时长(分钟)")
     * @ApiParams   (name="intention_city", type="string", required=false, description="意向城市")
     * @ApiParams   (name="is_training", type="integer", required=false, description="是否培训中")
     * @ApiParams   (name="switch", type="integer", required=false, description="是否启用")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function stopStaff()
    {
        $params = $this->request->post();

        // 必要参数验证
        if (empty($params['hair_id'])) {
            apijson(1, [], '缺少员工ID');
        }

        // 开始事务
        \think\Db::startTrans();

        try {
            // 更新数据
            $updateData = [];

            // 只更新提交的字段

            $allowFields = ['switch'];
            foreach ($allowFields as $field) {
                if (isset($params[$field])) {
                    $updateData[$field] = $params[$field];
                }
            }

            if (!empty($updateData)) {
                $updateData['update_time'] = time();

                $this->hairModel->where('id', $params['hair_id'])->update($updateData);

            }

            // 提交事务
            \think\Db::commit();

            apijson(0, [], '操作成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '操作失败: ' . $e->getMessage());
        }
    }


    /**
     * 未分配的发型师列表
     *
     */
    public function getHair()
    {
        $merchant_id = $this->request->post('merchant_id');
        $params = $this->request->post();
        if( !$merchant_id ){
            apijson(1,[],'缺少商户id');
        }
        $where = [];
        try {

            // 添加关键词搜索条件
            if (!empty($params['keyword'])) {
                $keyword = $params['keyword'];
                $where['hair.name|hair.mobile'] = ['like', "%{$keyword}%"];
            }
            // 意向城市筛选
            if (!empty($params['intention_city'])) {
                $intention_city = $params['intention_city'];
                $where['hair.intention_city'] = ['=', $intention_city];
            }
            $list = $this->hairModel->with(['user'])->where([
                'hair.merchant_id' => $merchant_id,
                'hair.is_verify' => ['=','2'],
            ])->where('hair.store_id',null)->where($where)->limit($this->page,$this->limit)->select();
            apijson(0,$list);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }

    }



    /**
     * user_id获取员工详情
     * @ApiTitle    (获取员工详情)
     * @ApiMethod   (POST)
     * @ApiParams   (name="id", type="integer", required=true, description="员工ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function getUserDetail()
    {
        $id = $this->request->post('id');

        if (empty($id)) {
            apijson(1, [], '缺少员工ID');
        }

        try {
            // 查询手机号
            $mobile = $this->userModel->where('id',$id)->value('mobile');
            // 查询员工信息
            $hair = $this->hairModel->where('mobile', $mobile)->find();

            if (!$hair) {
                apijson(1, [], '员工不存在');
            }

            // 获取员工等级名称
            $levelInfo = $this->hairLevelModel->where('value', $hair['level'])->find();
            $hair['level_name'] = $levelInfo ? $levelInfo['name'] : '';

            // 获取门店信息
            $store = $this->storeModel->where('id', $hair['store_id'])->find();
            $hair['store_name'] = $store ? $store['name'] : '';

            // 格式化时间
            $hair['create_time_text'] = date('Y-m-d H:i:s', $hair['create_time']);
            $hair['update_time_text'] = date('Y-m-d H:i:s', $hair['update_time']);

            // 获取员工的订单统计
//            $orderStats = $this->getStaffOrderStats($id);
//            $hair['order_stats'] = $orderStats;

            apijson(0, $hair, '获取成功');
        } catch (UploadException $e) {
            apijson(1, [], '获取失败: ' . $e->getMessage());
        }
    }



}
