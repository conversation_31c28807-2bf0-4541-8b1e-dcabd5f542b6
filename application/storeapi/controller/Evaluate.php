<?php

namespace app\storeapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\model\Evaluate as evaluateModel;
use app\model\store\Manage as storeModel;

/**
 * 评价
 */
class Evaluate extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    protected $evaluateModel;
    protected $storeModel;

    public function _initialize()
    {
        parent::_initialize();

        $this->evaluateModel = new evaluateModel();
        $this->storeModel = new storeModel();
    }

    /**
     * 评价列表
     *
     */
    public function getEvaluateList()
    {
        $merchant_id = $this->request->post('merchant_id');
        $store_id = $this->request->post('store_id');
        $is_type = $this->request->post('is_type');
        $time_range = $this->request->post('time_range')?$this->request->post('time_range'):'day';
        $start_time = $this->request->post('start_time', '');
        $end_time = $this->request->post('end_time', '');
        $satisfied_type = $this->request->post('satisfied_type', '');

        if (!$merchant_id) {
            apijson(1, [], '缺少商户id');
        }
        if ($is_type == 2 && !$store_id) {
            apijson(1, [], '缺少门店id');
        }
        if (!$is_type) {
            apijson(1, [], '缺少评价类型');
        }

        // 构建基础查询条件
        $where = [
            'merchant_id' => $merchant_id,
            'is_type' => $is_type,
            'is_del' => 1,
        ];
        if( $is_type == 2 && $store_id ){
            $where['store_id'] = $store_id;
        }

        if( !empty( $satisfied_type ) && $satisfied_type != 0 ){
            $where['satisfied_type'] = $satisfied_type;
        }
        // 根据时间范围筛选
        switch ($time_range) {
            case 'day': // 当日
                $today_start = strtotime(date('Y-m-d 00:00:00'));
                $today_end = strtotime(date('Y-m-d 23:59:59'));
                $where['create_time'] = ['between', [$today_start, $today_end]];
                break;
            case 'month': // 当月
                $month_start = strtotime(date('Y-m-01 00:00:00'));
                $month_end = strtotime(date('Y-m-t 23:59:59'));
                $where['create_time'] = ['between', [$month_start, $month_end]];
                break;
            case 'year': // 当年
                $year_start = strtotime(date('Y-01-01 00:00:00'));
                $year_end = strtotime(date('Y-12-31 23:59:59'));
                $where['create_time'] = ['between', [$year_start, $year_end]];
                break;
            case 'custom': // 自定义时间范围
                if ($start_time && $end_time) {
                    $start_timestamp = strtotime($start_time);
                    $end_timestamp = strtotime($end_time) + 86399; // 加上一天减一秒，使结束日期包含当天
                    $where['create_time'] = ['between', [$start_timestamp, $end_timestamp]];
                }
                break;
            default:
                // 不添加时间筛选
                break;
        }

        try {
            // 获取门店名称
            $store_name = $this->storeModel->where('id', $store_id)->value('name');
            // 查询评价列表
            $list = $this->evaluateModel
                ->where($where)
                ->order('create_time', 'desc')
                ->limit($this->page, $this->limit)
                ->select();
            // 处理评价数据
            foreach ($list as &$item) {
                // 添加用户信息
                $user = \think\Db::name('user')->where('id', $item['user_id'])->find();
                $item['user_info'] = [
                    'nickname' => $user ? $user['nickname'] : '',
                    'avatar' => $user ? cdnurl($user['avatar'], true) : ''
                ];

                // 处理图片和标签
                $item['images_list'] = !empty($item['images']) ? explode(',', $item['images']) : [];
                $item['labels_list'] = !empty($item['labels']) ? explode(',', $item['labels']) : [];

                // 处理追加评价
                if ($item['is_append'] == 1) {
                    $item['append_images_list'] = !empty($item['append_images']) ? explode(',', $item['append_images']) : [];
                }

                // 根据评价类型添加不同的关联信息
                if ($is_type == 1) { // 商品评价
                    // 添加商品信息
                    if ($item['goods_id']) {
                        $goods = \think\Db::name('delivery_goods')->where('id', $item['goods_id'])->find();
                        $item['goods_info'] = [
                            'id' => $goods ? $goods['id'] : 0,
                            'name' => $goods ? $goods['name'] : '',
                            'image' => $goods ? cdnurl($goods['cimage'], true) : '',
                            'price' => $goods ? $goods['price'] : 0
                        ];
                    } else {
                        $item['goods_info'] = null;
                    }
                } else if ($is_type == 2) { // 理发评价
                    // 添加理发师信息
                    if ($item['hair_id']) {
                        $hair = \think\Db::name('user')->where('id', $item['hair_id'])->find();
                        $item['hair_info'] = [
                            'id' => $hair ? $hair['id'] : 0,
                            'name' => $hair ? $hair['nickname'] : '',
                            'avatar' => $hair ? cdnurl($hair['avatar'], true) : '',
                            'level' => $hair ? $hair['level'] : ''
                        ];
                    } else {
                        $item['hair_info'] = null;
                    }
                }

                // 格式化时间
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                if ($item['is_append'] == 1 && $item['append_time']) {
                    $item['append_time_text'] = date('Y-m-d H:i:s', $item['append_time']);
                }

                // 添加商家回复信息
                if ($item['is_reply'] == 1) {
                    $item['reply_time_text'] = date('Y-m-d H:i:s', $item['reply_time']);
                }
            }
            $dataList = [];
            foreach ( $list as $key => $value ) {
                $dataList[$key]['user'] = $value['user_info']['nickname'];
                $dataList[$key]['completeTime'] = $value['create_time_text'];
                $dataList[$key]['hairStylist'] = $value['hair_info']['name'];
                $dataList[$key]['type'] = $value['content'];
                $dataList[$key]['amount'] = $value['labels'];
                $dataList[$key]['commission'] = $value['status'] == 1 ? '显示' : '隐藏';
                $dataList[$key]['id'] = $value['id'];
                $dataList[$key]['status'] = $value['status'];
                $dataList[$key]['is_reply'] = $value['is_reply'];
            }
            $data['store_name'] = $store_name;
            $data['list'] = $dataList;
//            echo "<pre>";
//            print_r( $data );
//            echo "<pre>";
//            die;
            apijson(0, $data);
        } catch (UploadException $e) {
            apijson(1, [], '获取评价列表失败: ' . $e->getMessage());
        }
    }



    /**
     * 回复评价
     *
     * @ApiTitle    (回复评价)
     * @ApiMethod   (POST)
     * @ApiParams   (name="evaluate_id", type="integer", required=true, description="评价ID")
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=true, description="门店ID")
     * @ApiParams   (name="reply_content", type="string", required=true, description="回复内容")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function replyEvaluate()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必要参数
        $requiredFields = ['evaluate_id', 'merchant_id', 'store_id', 'reply_content'];
        foreach ($requiredFields as $field) {
            if (empty($post[$field])) {
                apijson(1, [], "缺少必要参数: {$field}");
            }
        }

        // 查找评价
        $evaluate = $this->evaluateModel->where([
            'id' => $post['evaluate_id'],
            'merchant_id' => $post['merchant_id'],
            'is_del' => 1
        ])->find();

        if (!$evaluate) {
            apijson(1, [], '未找到该评价');
        }

        // 检查是否已回复
        if ($evaluate['is_reply'] == 1) {
            apijson(1, [], '该评价已回复，不能重复回复');
        }

        // 开始事务
        \think\Db::startTrans();

        try {
            // 更新评价，标记为已回复
            $updateData = [
                'is_reply' => 1,
                'reply_time' => time(),
                'reply_content' => $post['reply_content']
            ];

            $result = $this->evaluateModel->where('id', $evaluate['id'])->update($updateData);

            if (!$result) {
                apijson(1, [], '回复评价失败');
            }

            // 提交事务
            \think\Db::commit();

            // 获取更新后的评价信息
            $updatedEvaluate = $this->evaluateModel->where('id', $evaluate['id'])->find();

            apijson(0, $updatedEvaluate, '回复评价成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '回复评价失败: ' . $e->getMessage());
        }
    }

    /**
     * 隐藏评价
     *
     * @ApiTitle    (隐藏评价)
     * @ApiMethod   (POST)
     * @ApiParams   (name="evaluate_id", type="integer", required=true, description="评价ID")
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=true, description="门店ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function hideEvaluate()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必要参数
        $requiredFields = ['evaluate_id', 'merchant_id', 'store_id'];
        foreach ($requiredFields as $field) {
            if (empty($post[$field])) {
                apijson(1, [], "缺少必要参数: {$field}");
            }
        }

        // 查找评价
        $evaluate = $this->evaluateModel->where([
            'id' => $post['evaluate_id'],
            'merchant_id' => $post['merchant_id'],
            'is_del' => 1
        ])->find();

        if (!$evaluate) {
            apijson(1, [], '未找到该评价');
        }

        // 开始事务
        \think\Db::startTrans();

        try {
            // 更新评价状态为隐藏
            $updateData = [
                'status' => 2  // 假设2表示隐藏状态
            ];

            $result = $this->evaluateModel->where('id', $evaluate['id'])->update($updateData);

            if (!$result) {
                apijson(1, [], '隐藏评价失败');
            }

            // 提交事务
            \think\Db::commit();

            // 获取更新后的评价信息
            $updatedEvaluate = $this->evaluateModel->where('id', $evaluate['id'])->find();

            apijson(0, $updatedEvaluate, '隐藏评价成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '隐藏评价失败: ' . $e->getMessage());
        }
    }



}
