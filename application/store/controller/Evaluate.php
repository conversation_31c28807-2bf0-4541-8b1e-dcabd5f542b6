<?php

namespace app\store\controller;

use app\common\controller\StoreBackend;
use Exception;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 评价管理
 *
 * @icon fa fa-circle-o
 */
class Evaluate extends StoreBackend
{

    /**
     * Evaluate模型对象
     * @var \app\model\Evaluate
     */
    protected $model = null;
    protected $loginList = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\model\Evaluate;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("isTypeList", $this->model->getIsTypeList());
        $this->view->assign("isDelList", $this->model->getIsDelList());
        $this->loginList = LoginList('store');
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['merchant','manage','user'])
                    ->where($where)
                    ->where('evaluate.merchant_id',$this->loginList['merchant_id'])
                    ->where('evaluate.store_id',$this->loginList['store_id'])
                    ->order($sort, $order)
                    ->paginate($limit);
//            echo "<pre>";
//            print_r( $this->model->getLastSql() );
//            echo "<pre>";die;
            foreach ($list as $row) {
                $row->visible(['id','vote_1','vote_2','vote_3','vote_4','vote_5','content','status','create_time','is_type','is_del','is_reply']);
                $row->visible(['merchant']);
				$row->getRelation('merchant')->visible(['merchant_name']);
				$row->visible(['manage']);
				$row->getRelation('manage')->visible(['name']);
				$row->visible(['user']);
				$row->getRelation('user')->visible(['nickname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 回复
     *
     * @return string
     * @throws \think\Exception
     */
    public function reply($ids = null )
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $params['pid'] = $ids;
            $params['is_reply'] = 1;
            $params['reply_time'] = time();
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 详情
     */
    public function detail($ids)
    {
        $row = $this->model->get(['id' => $ids]);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if (!$this->auth->isSuperAdmin()) {
            if (!$row['admin_id'] || !in_array($row['admin_id'], $this->childrenAdminIds)) {
                $this->error(__('You have no permission'));
            }
        }

        $this->view->assign("row", $row);
        $this->view->assign("type", $row->is_type);
        return $this->view->fetch();
    }

}
