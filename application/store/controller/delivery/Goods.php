<?php

namespace app\store\controller\delivery;

use app\common\controller\StoreBackend;
use think\Collection;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 发货订单管理
 *
 * @icon fa fa-circle-o
 */
class Goods extends StoreBackend
{

    /**
     * Subjects模型对象
     * @var \app\addons\delivery\model\Goods
     */
    protected $model = null;
    protected $catModel = null;
    protected $merchantModel = null;
    protected $loginList = null;
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model =model('addons\delivery\model\Goods');
        $this->catModel =model('addons\delivery\model\Cat');
        $this->merchantModel = new \app\model\Merchant;
        // 获取登陆信息
        $LoginList = LoginList('store');
        $this->view->assign("catList", $this->model->getCatList($LoginList['merchant_id']));
        $this->view->assign("recommendList", $this->model->getRecommendList());
        $this->view->assign("listList", $this->model->getListList());
        $this->view->assign("hotList", $this->model->getHostList());
        $this->loginList = LoginList('store');
    }

    public function import()
    {
        parent::import();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->where('goods.merchant_id',$this->loginList['merchant_id'])
                ->field(true)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $row){


            }

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        // 获取登陆信息
        $LoginList = LoginList('store');
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                //$params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }

                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $params['merchant_id'] = $LoginList['merchant_id'];
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        //存发货人
                        $params['delivery_id']=$this->model->id;
                        $result= model('addons\delivery\model\ExpressBillSender')->allowField(true)->save($params);
                    }
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
       $postageRules= model('addons\delivery\model\PostageRules')->order('is_default asc')->select();
        if(empty($postageRules)){
            $postageRules=[['id'=>0,'name'=>'无']];
        }
       $this->assign('postageRules',$postageRules);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        // 获取登陆信息
        $LoginList = LoginList('store');
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                //$params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $params['merchant_id'] = $LoginList['merchant_id'];
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        $model=model('addons\delivery\model\ExpressBillSender');
                        if(!empty($params['sender_id'])){
                            //修改发货人
                            $sender_row=$model->get($params['sender_id']);
                            $result = $sender_row->allowField(true)->save($params);
                        }else{
                            $params['delivery_id']=$row->id;
                            $result= $model->allowField(true)->save($params);
                        }

                    }
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assign('row',$row);
        $postageRules= model('addons\delivery\model\PostageRules')->order('is_default asc')->select();
        if(empty($postageRules)){
            $postageRules=[['id'=>0,'name'=>'无']];
        }
        $this->assign('postageRules',$postageRules);
        return $this->view->fetch();
    }

    /**
     * 添加分类
     */
    public function addcat()
    {
        // 获取登陆信息
        $LoginList = LoginList('store');
        $this->model=model('addons\delivery\model\Cat');
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                //$params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $params['merchant_id'] = $LoginList['merchant_id'];
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    // 获取二级分类信息
    public function catTwoList(){
        $pid = $this->request->post('pid');
        $list = $this->catModel->where('pid',$pid)->field('id,name')->select();
        if ( !empty( $list ) ){
            $result = array("code" => 200, "rows" => $list);
        }else{
            $result = array("code" => 201, "rows" => []);
        }
        return json($result);
    }

}
