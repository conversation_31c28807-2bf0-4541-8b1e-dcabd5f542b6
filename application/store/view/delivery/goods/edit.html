<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('商品分类')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <select  id="c-lessonname" data-rule="required" class="form-control selectpicker" name="row[cat_id]">
                    {foreach name="catList" item="vo"}
                    <option value="{$vo.id}" {in name="vo.id" value="$row.cat_id"}selected{/in}>{$vo.name}</option>
                    {/foreach}
                </select>
                <div class="input-group-addon no-border no-padding">
                    <span><button href="{:url('delivery.goods/addcat')}" type="button" id="select-resources" class="btn btn-primary" data-input-id="c-express"><i class="fa fa-list"></i> {:__('新增')}</button></span>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('商品名称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('售价')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" data-rule="required" class="form-control" name="row[price]" type="text" value="{$row.price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('积分')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-points" type="text" class="form-control" name="row[points]" value="{$row.points|htmlentities}"  />
        </div>
    </div>
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">规格:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-specifications" type="text" data-rule="required" class="form-control" name="row[specifications]" value="{$row.specifications|htmlentities}"  />
        </div>
    </div>
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">单位:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unit" type="text" data-rule="required" class="form-control" name="row[unit]" value="{$row.unit|htmlentities}"  />
        </div>
    </div>
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">销量:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sales_colume" type="text" data-rule="required" class="form-control" name="row[sales_colume]" value="{$row.sales_colume|htmlentities}"  />
        </div>
    </div>
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">库存:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-inventory" type="text" data-rule="required" class="form-control" name="row[inventory]" value="{$row.inventory|htmlentities}"  />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('运费规则')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-xinzhi" data-rule="required" class="form-control selectpicker" name="row[freight]">
                {foreach name="postageRules" item="vo"}
                <option value="{$vo.id}" {in name="vo.id" value="$row.freight"}selected{/in}>{$vo.name}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('商品缩略图')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-cimage" data-rule="required" class="form-control" size="50" name="row[cimage]" type="text" value="{$row.cimage}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-cimage" class="btn btn-danger plupload" data-input-id="c-cimage" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-cimage"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-cimage" class="btn btn-primary fachoose" data-input-id="c-cimage" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-cimage"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-cimage"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('商品图片')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-cimages" class="form-control" size="50" name="row[cimages]" type="text" value="{$row.cimages}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-cimages" class="btn btn-danger plupload" data-input-id="c-cimages" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="true" data-preview-id="p-cimages"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-cimages" class="btn btn-primary fachoose" data-input-id="c-cimages" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-cimages"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-cimages"></ul>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">推荐:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-is_recommend" class="form-control selectpicker" name="row[is_recommend]">
                {foreach name="recommendList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_recommend"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">上架:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-is_list" class="form-control selectpicker" name="row[is_list]">
                {foreach name="listList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_list"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">热销:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-is_hot" class="form-control selectpicker" name="row[is_hot]">
                {foreach name="hotList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_hot"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
