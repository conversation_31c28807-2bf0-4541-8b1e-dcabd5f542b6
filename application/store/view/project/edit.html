<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-merchant_id" min="0" class="form-control selectpicker" name="row[merchant_id]">
                {foreach name="merchantList" item="vo"}
                <option value="{$key}" {if condition="$key eq $row.merchant_id"}selected{/if}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('发型师')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-hair_id" min="0" data-rule="required" class="form-control selectpicker" name="row[hair_id]">
                <option value="" >请选择发型师</option>
                {foreach name="hairList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.hair_id"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-store_id" min="0" class="form-control selectpicker" name="row[store_id]">
                {foreach name="storeMangeList" item="vo"}
                <option value="{$key}" {if condition="$key eq $row.store_id"}selected{/if}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('项目类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-type" min="0" data-rule="required" class="form-control selectpicker" name="row[type]">
                <option value="" >{$row.type|htmlentities}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" class="form-control" step="0.01" name="row[money]" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('活动价')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-activity_money" class="form-control" step="0.01" name="row[activity_money]" value="{$row.activity_money|htmlentities}" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Label')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-label" class="form-control editor" rows="5" name="row[label]" cols="50">{$row.label|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_duration')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_duration" class="form-control" name="row[service_duration]" type="text" value="{$row.service_duration|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('折扣（折）')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-discount" placeholder="注:8折输入8即可，9.5折 输入9.5即可" data-rule="required" class="form-control" name="row[discount]" type="text" value="{$row.discount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('折扣开始时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-start_time" placeholder="折扣为空时 开始时间和结束时间不需要选择" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[start_time]" type="text" value="{:$row.start_time?datetime($row.start_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('折扣结束时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[end_time]" type="text" value="{:$row.end_time?datetime($row.end_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_card')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-is_card" class="form-control selectpicker" name="row[is_card]">
                {foreach name="IsCardList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_card"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:$row.create_time?datetime($row.create_time):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
