<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-sex" min="0" class="form-control selectpicker" name="row[merchant_id]">
                {foreach name="merchantList" item="vo"}
                <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" class="form-control" name="row[address]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Business_hours_time1')}:</label>
        <!--        <div class="col-xs-12 col-sm-8">-->
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_start_time]"
                   data-plugin="datetimepicker"
                   data-range="true">

        </div>
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_end_time]"
                   data-plugin="datetimepicker"
                   data-range="true">

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Business_hours_time2')}:</label>
        <!--        <div class="col-xs-12 col-sm-8">-->
        <!--            <input id="c-business_hours_time2" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true" name="row[business_hours_time2]" type="text" value="{:date('Y-m-d H:i:s')}">-->
        <!--        </div>-->
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_start_time2]"
                   data-plugin="datetimepicker"
                   data-range="true">

        </div>
        <div class="col-xs-6 col-sm-4">
            <!--            <input id="c-business_hours_time1" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-use-current="true"  name="row[business_hours_time1]" type="text" value="{:date('H:i:s')} - {:date('H:i:s')}">-->
            <input type="text"
                   class="form-control datetimepicker"
                   data-date-format="HH:mm:ss"
                   data-use-current="true"
                   name="row[business_hours_end_time2]"
                   data-plugin="datetimepicker"
                   data-range="true">

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_user_id')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-store-user-id" min="0" class="form-control selectpicker" name="row[store_user_id]">
                {foreach name="storeUser" item="vo"}
                <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" class="form-control" name="row[mobile]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Switch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-switch" class="form-control selectpicker" name="row[switch]">
                {foreach name="statusList" item="vo"}
                <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>

    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
