<?php

namespace app\hairapi\controller\store;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Config;
use think\Validate;

/**
 * 门店接口
 */
class Store extends Api
{
    protected $noNeedLogin = ['index','details'];
    protected $noNeedRight = '*';
    protected $storeModel = null;
    protected $hairModel = null;
    protected $orderHairdressingModel = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->storeModel = new \app\model\store\Manage;
        $this->hairModel = new \app\model\verify\Hair;
        $this->orderHairdressingModel = new \app\model\order\Hairdressing;
    }

    /**
     * 门店列表
     */
    public function index()
    {
        $merchant_id = $this->request->post('merchant_id');
        if( !$merchant_id ){
            apijson(1,[],'缺少商户id');
        }

        try {
            $list = $this->storeModel->where('merchant_id',$merchant_id)->limit($this->page,$this->limit)->select();
            foreach ($list as &$item) {
                $item['hari_count'] = $this->hairModel->where([
                    'merchant_id' => $merchant_id,
                    'store_id' => $item['id']
                ])->count();
                $item['order_count'] = $this->orderHairdressingModel->where([
                    'merchant_id' => $merchant_id,
                    'store_id' => $item['id']
                ])->count();
            }
            apijson(0,$list);
        } catch (UploadException $e) {
            $this->error($e->getMessage());
        }
    }
    /**
     * 门店详情
     */
    public function details()
    {
        $store_id = $this->request->post('store_id');
        if( !$store_id ){
            apijson(1,[],'缺少门店id');
        }

        try {
            $list = $this->storeModel->where([
                'id' => $store_id
            ])->find();
            apijson(0,$list);
        } catch (UploadException $e) {
            $this->error($e->getMessage());
        }
    }

}
