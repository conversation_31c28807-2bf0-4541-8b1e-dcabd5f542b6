<?php

namespace app\hairapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\model\order\Hairdressing as hairdressingModel;
use app\model\store\Manage as storeModel;
use app\model\finance\ShareRecord as incomeModel;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = [];
    protected $hairdressingModel = null;
    protected $storeModel = null;
    protected $incomeModel = null;
    public function _initialize()
    {
        parent::_initialize();
        $this->hairdressingModel    = new hairdressingModel();
        $this->storeModel           = new storeModel();
        $this->incomeModel          = new incomeModel();
    }
    /**
     * 首页统计
     *
     */
    public function index()
    {
        $post = $this->request->post();
        if( empty( $post['merchant_id'] ) || empty( $post['store_id'] || empty( $post['hair_id'] ) ) ){
            apijson(1,[],'缺少参数');
        }
        try {
            // 获取门店名称
            $list['store_name'] = $this->storeModel->where([
                'merchant_id' => $post['merchant_id'],
                'id' => $post['store_id'],
            ])->value('name');
            $start_time = strtotime(date('Y-m-d 00:00:00',time()));
            $end_time = strtotime(date('Y-m-d 23:59:59',time()));
            $where['merchant_id'] = $post['merchant_id'];
            $where['store_id'] = $post['store_id'];
            $where['hair_id'] = $post['hair_id'];
            // 获取今日预约数
            $list['reservation_count'] = $this->hairdressingModel->where($where)->where([
                'status' => 1,
                'reservation_time' => ['between', [$start_time, $end_time]]
            ])->count();
            // 获取今日核销数
            $list['write_off_count'] = $this->hairdressingModel->where($where)->where([
                'status' => 2,
                'hair_write_off_time' => ['between', [$start_time, $end_time]]
            ])->count();
            $sevenTimeList = getRecentSevenDaysRange();
            // 获取近7天的预约数
            $list['seven_reservation_count'] = $this->hairdressingModel->where($where)->where([
                'reservation_time' => ['between', [$sevenTimeList['start_time'], $sevenTimeList['end_time']]]
            ])->count();
            // 获取历史预约数
            $list['history_reservation_count'] = $this->hairdressingModel->where($where)->count();
            // 获取历史核销数
            $list['history_write_off_count'] = $this->hairdressingModel->where($where)->where(['status' => 2])->count();
            // 我的收益 && 累计收益
            $list['income'] = $this->incomeModel->where($where)->sum('share_money');
            // 近7天收益
            $list['income_reservation_count'] = $this->incomeModel->where( $where )->where([
                'create_time' => ['between', [$sevenTimeList['start_time'], $sevenTimeList['end_time']]]
            ])->sum('share_money');
            apijson(0,$list,'获取成功');
        } catch ( UploadException $e ){
            apijson(1,[],'获取失败');
        }
    }

}
