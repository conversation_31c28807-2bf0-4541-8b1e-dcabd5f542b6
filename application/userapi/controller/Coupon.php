<?php

namespace app\userapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\model\data\hair\Level as hairLevelModel;
use app\model\marketing\coupon\Record as RecordModel;
use app\model\marketing\coupon\Coupon as CouponModel;
use app\model\Project as projectModel;
use app\model\store\Manage as storeModel;

/**
 * 优惠券管理
 */
class Coupon extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    protected $RecordModel;
    protected $CouponModel;
    protected $storeModel;
    protected $projectModel;
    protected $hairLevelModel;

    public function _initialize()
    {
        parent::_initialize();
        $this->RecordModel = new RecordModel();
        $this->CouponModel = new CouponModel();
        $this->storeModel = new storeModel();
        $this->projectModel = new projectModel();
        $this->hairLevelModel = new hairLevelModel;
    }

    /**
     * 我的优惠券列表
     *
     */
    public function getList()
    {
        $data = $this->request->post();
        if( empty( $data['merchant_id']) || empty( $data['user_id'] ) ){
            apijson(1,[],'缺少参数');
        }
        $where['record.merchant_id'] = $data['merchant_id'];
        $where['record.user_id'] = $data['user_id'];
        if( !empty( $data['is_use'] ) ){
            $where['is_use'] = $data['is_use'];
        }
        try {
            $list = $this->RecordModel->with(['coupon'])->where($where)->limit($this->page,$this->limit)->select();
            if( !empty( $list ) ){
                foreach ( $list as &$value){
                    // 使用范围
                    if( !empty( $value['coupon']['use_ids'] ) ){
                        // 获取门店名称
                        $store_name = $this->storeModel->where('id','in',$value['coupon']['use_ids'])->column('name');
                        $value['coupon']['use_ids'] = implode(',',$store_name);
                    }else{
                        $value['coupon']['use_ids'] = '所有门店通用';
                    }
                    // 门店范围
                    if( !empty( $value['coupon']['store_ids'] ) ){
                        // 获取门店名称
                        $store_name = $this->storeModel->where('id','in',$value['coupon']['store_ids'])->column('name');
                        $value['coupon']['store_ids'] = implode(',',$store_name);
                    }else{
                        $value['coupon']['store_ids'] = '所有门店通用';
                    }
                    // 不可用服务项目
                    if( !empty( $value['coupon']['no_project_ids'] ) ){
                        // 获取服务项目名称
                        $project_name = $this->projectModel->where('id','in',$value['coupon']['no_project_ids'])->column('name');
                        $value['coupon']['no_project_ids'] = implode(',',$project_name);
                    }else{
                        $value['coupon']['no_project_ids'] = '所有服务项目通用';
                    }
                    // 权益叠加
                    if( !empty( $value['coupon']['is_stacking'] ) ){
                        $value['coupon']['is_stacking'] = '可与其他折扣叠加使用';
                    }else{
                        $value['coupon']['is_stacking'] = '不可与其他折扣叠加使用';
                    }
                    // 可用发型师级别
                    if( !empty( $value['coupon']['hair_level_scope'] ) ){
                        // 获取发型师名称
                        $hair_level_name = $this->hairLevelModel->where('value','in',$value['coupon']['hair_level_scope'])->column('name');
                        $value['coupon']['hair_level_scope'] = implode(',',$hair_level_name);
                    }else{
                        $value['coupon']['hair_level_scope'] = '所有发型师通用';
                    }
                }
                
            }
            apijson(0,$list);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }

    }

    /**
     * 我的优惠券列表 -- 订单调用
     *
     */
    public function getOrderList()
    {
        $data = $this->request->post();
        if( empty( $data['merchant_id']) || empty( $data['user_id']  || empty( $data['store_id'] )  || empty( $data['project_id'] ) )){
            apijson(1,[],'缺少参数');
        }
        $where['record.merchant_id'] = $data['merchant_id'];
        $where['record.user_id'] = $data['user_id'];
        if( !empty( $data['is_use'] ) ){
            $where['is_use'] = $data['is_use'];
        }
        try {
            $list = $this->RecordModel->with(['coupon'])->where($where)->limit($this->page,$this->limit)->select();
            if( !empty( $list ) ){
                foreach ( $list as $key => &$value){
                    // 使用范围
                    if( !empty( $value['coupon']['use_ids'] ) ){
                        $use_ids_arr = explode(',',$value['coupon']['use_ids'] );
                        if( !in_array( $data['store_id'], $use_ids_arr ) ){
                            unset( $list[$key] );
                            continue;
                        }
                        // 获取门店名称
                        $store_name = $this->storeModel->where('id','in',$value['coupon']['use_ids'])->column('name');
                        $value['coupon']['use_ids'] = implode(',',$store_name);
                    }else{
                        $value['coupon']['use_ids'] = '所有门店通用';
                    }
                    // 门店范围
                    if( !empty( $value['coupon']['store_ids'] ) ){
                        $store_ids_arr = explode(',',$value['coupon']['store_ids'] );
                        if( !in_array( $data['store_id'], $store_ids_arr ) ){
                            unset( $list[$key] );
                            continue;
                        }
                        // 获取门店名称
                        $store_name = $this->storeModel->where('id','in',$value['coupon']['store_ids'])->column('name');
                        $value['coupon']['store_ids'] = implode(',',$store_name);
                    }else{
                        $value['coupon']['store_ids'] = '所有门店通用';
                    }
                    // 不可用服务项目
                    if( !empty( $value['coupon']['no_project_ids'] ) ){
                        $no_project_ids_arr = explode(',',$value['coupon']['no_project_ids'] );
                        if( !in_array( $data['project_id'], $no_project_ids_arr ) ){
                            unset( $list[$key] );
                            continue;
                        }
                        // 获取服务项目名称
                        $project_name = $this->projectModel->where('id','in',$value['coupon']['no_project_ids'])->column('name');
                        $value['coupon']['no_project_ids'] = implode(',',$project_name);
                    }else{
                        $value['coupon']['no_project_ids'] = '所有服务项目通用';
                    }
                    // 权益叠加
                    if( !empty( $value['coupon']['is_stacking'] ) && $value['coupon']['is_stacking'] == 1){
                        $value['coupon']['is_stacking'] = '可与其他折扣叠加使用';
                    }else{
                        $value['coupon']['is_stacking'] = '不可与其他折扣叠加使用';
                    }
                    // 可用发型师级别
                    if( !empty( $value['coupon']['hair_level_scope'] ) ){
                        // 获取发型师名称
                        $hair_level_name = $this->hairLevelModel->where('value','in',$value['coupon']['hair_level_scope'])->column('name');
                        $value['coupon']['hair_level_scope'] = implode(',',$hair_level_name);
                    }else{
                        $value['coupon']['hair_level_scope'] = '所有发型师通用';
                    }
                }

            }
            apijson(0,$list);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }

    }


    /**
     * 优惠券详情
     *
     */
    public function getDetails()
    {
        $data = $this->request->post();
        if( empty( $data['coupon_id'] ) ){
            apijson(1,[],'缺少参数');
        }
        $where['id'] = $data['coupon_id'];

        try {
            $list = $this->CouponModel->where($where)->find();
            apijson(0,$list);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }

    }


    /**
     * 第三方团购券核销
     * @ApiTitle    (第三方团购券核销)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=true, description="门店ID")
     * @ApiParams   (name="operator_id", type="integer", required=true, description="操作员ID")
     * @ApiParams   (name="code", type="string", required=true, description="兑换码")
     * @ApiParams   (name="platform", type="string", required=true, description="平台类型，douyin=抖音，meituan=美团")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function verifyThirdParty()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();
        if (empty($post['merchant_id']) || empty($post['store_id']) ||
            empty($post['user_id']) || empty($post['code']) || empty($post['platform'])) {
            apijson(1, [], '缺少必要参数');
        }

        // 验证平台类型
        if (!in_array($post['platform'], ['douyin', 'meituan'])) {
            apijson(1, [], '不支持的平台类型');
        }

        try {
            // 根据平台类型调用不同的核销接口
            if ($post['platform'] == 'douyin') {
                $result = $this->verifyDouyinCoupon($post);
            } else {
                $result = $this->verifyMeituanCoupon($post);
            }

            if ($result['success']) {
                // 记录核销记录
                $this->recordVerifyLog($post, $result['data']);
                apijson(0, $result['data'], '核销成功');
            } else {
                apijson(1, [], $result['message']);
            }
        } catch (\Exception $e) {
            apijson(1, [], '核销失败: ' . $e->getMessage());
        }
    }

    /**
     * 抖音团购券核销
     * @param array $params 请求参数
     * @return array 核销结果
     */
    private function verifyDouyinCoupon($params)
    {
        // 获取商户的抖音配置信息
        $config = \app\model\ThirdPartyConfig::where([
            'merchant_id' => $params['merchant_id'],
            'platform' => 'douyin'
        ])->find();

        if (!$config) {
            return ['success' => false, 'message' => '未配置抖音团购券接口信息'];
        }

        // 构建请求参数
        $requestData = [
            'app_id' => $config['app_id'],
            'code' => $params['code'],
            'store_id' => $params['store_id'],
            'timestamp' => time(),
            'nonce_str' => $this->generateNonceStr()
        ];

        // 生成签名
        $requestData['sign'] = $this->generateDouyinSign($requestData, $config['app_secret']);

        // 发送请求到抖音验券接口
        $url = 'https://open.douyin.com/api/trade/voucher/verify';
        $response = \fast\Http::sendRequest($url, $requestData, 'POST', [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);

        if ($response['ret']) {
            $result = json_decode($response['msg'], true);

            if (isset($result['err_no']) && $result['err_no'] == 0) {
                // 核销成功
                $couponData = $result['data'];
                return [
                    'success' => true,
                    'data' => [
                        'platform' => 'douyin',
                        'platform_text' => '抖音',
                        'code' => $params['code'],
                        'name' => $couponData['title'] ?? '抖音团购券',
                        'amount' => $couponData['amount'] ?? 0,
                        'order_id' => $couponData['order_id'] ?? '',
                        'verify_time' => time(),
                        'verify_time_text' => date('Y-m-d H:i:s'),
                        'raw_data' => $couponData
                    ]
                ];
            } else {
                // 核销失败
                return [
                    'success' => false,
                    'message' => $result['err_tips'] ?? '抖音验券失败'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => '抖音接口请求失败'
            ];
        }
    }

    /**
     * 美团团购券核销
     * @param array $params 请求参数
     * @return array 核销结果
     */
    private function verifyMeituanCoupon($params)
    {
        // 获取商户的美团配置信息
        $config = \app\model\ThirdPartyConfig::where([
            'merchant_id' => $params['merchant_id'],
            'platform' => 'meituan'
        ])->find();

        if (!$config) {
            return ['success' => false, 'message' => '未配置美团团购券接口信息'];
        }

        // 构建请求参数
        $requestData = [
            'app_key' => $config['app_key'],
            'timestamp' => time(),
            'coupon_code' => $params['code'],
            'poi_id' => $config['poi_id'], // 美团门店ID
            'deal_id' => '', // 可选，团购ID
            'random' => mt_rand(100000, 999999)
        ];

        // 生成签名
        $requestData['sign'] = $this->generateMeituanSign($requestData, $config['app_secret']);

        // 发送请求到美团验券接口
        $url = 'https://openapi.meituan.com/api/deal/verification/verify';
        $response = \fast\Http::sendRequest($url, $requestData, 'POST', [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);

        if ($response['ret']) {
            $result = json_decode($response['msg'], true);

            if (isset($result['status']) && $result['status'] == 0) {
                // 核销成功
                $couponData = $result['data'];
                return [
                    'success' => true,
                    'data' => [
                        'platform' => 'meituan',
                        'platform_text' => '美团',
                        'code' => $params['code'],
                        'name' => $couponData['deal_title'] ?? '美团团购券',
                        'amount' => $couponData['deal_price'] ?? 0,
                        'order_id' => $couponData['order_id'] ?? '',
                        'verify_time' => time(),
                        'verify_time_text' => date('Y-m-d H:i:s'),
                        'raw_data' => $couponData
                    ]
                ];
            } else {
                // 核销失败
                return [
                    'success' => false,
                    'message' => $result['message'] ?? '美团验券失败'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => '美团接口请求失败'
            ];
        }
    }

    /**
     * 记录核销日志
     * @param array $params 请求参数
     * @param array $couponData 券数据
     */
    private function recordVerifyLog($params, $couponData)
    {
        // 记录核销记录
        $verifyData = [
            'merchant_id' => $params['merchant_id'],
            'store_id' => $params['store_id'],
            'operator_id' => $params['operator_id'],
            'platform' => $params['platform'],
            'code' => $params['code'],
            'name' => $couponData['name'] ?? '',
            'amount' => $couponData['amount'] ?? 0,
            'order_id' => $couponData['order_id'] ?? '',
            'verify_time' => time(),
            'raw_data' => json_encode($couponData['raw_data'] ?? [], JSON_UNESCAPED_UNICODE),
            'remark' => $params['platform'] == 'douyin' ? '抖音团购券核销' : '美团团购券核销'
        ];

        // 保存核销记录
        \app\model\marketing\coupon\ThirdPartyVerify::create($verifyData);
    }

    /**
     * 生成随机字符串
     * @param int $length 长度
     * @return string 随机字符串
     */
    private function generateNonceStr($length = 16)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    /**
     * 生成抖音签名
     * @param array $params 参数
     * @param string $appSecret 密钥
     * @return string 签名
     */
    private function generateDouyinSign($params, $appSecret)
    {
        ksort($params);
        $stringA = '';
        foreach ($params as $key => $value) {
            if ($key != 'sign' && $value !== '' && !is_array($value)) {
                $stringA .= $key . '=' . $value . '&';
            }
        }
        $stringA = rtrim($stringA, '&');
        $stringSignTemp = $stringA . '&key=' . $appSecret;
        return strtoupper(md5($stringSignTemp));
    }

    /**
     * 生成美团签名
     * @param array $params 参数
     * @param string $appSecret 密钥
     * @return string 签名
     */
    private function generateMeituanSign($params, $appSecret)
    {
        ksort($params);
        $stringA = '';
        foreach ($params as $key => $value) {
            if ($key != 'sign' && $value !== '' && !is_array($value)) {
                $stringA .= $key . '=' . $value . '&';
            }
        }
        $stringA = rtrim($stringA, '&');
        $stringSignTemp = $stringA . $appSecret;
        return md5($stringSignTemp);
    }

    /**
     * 查询第三方团购券信息
     * @ApiTitle    (查询第三方团购券信息)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="code", type="string", required=true, description="兑换码")
     * @ApiParams   (name="platform", type="string", required=true, description="平台类型，douyin=抖音，meituan=美团")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function queryThirdParty()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();
        if (empty($post['merchant_id']) || empty($post['code']) || empty($post['platform'])) {
            apijson(1, [], '缺少必要参数');
        }

        // 验证平台类型
        if (!in_array($post['platform'], ['douyin', 'meituan'])) {
            apijson(1, [], '不支持的平台类型');
        }

        try {
            // 根据平台类型调用不同的查询接口
            if ($post['platform'] == 'douyin') {
                $result = $this->queryDouyinCoupon($post);
            } else {
                $result = $this->queryMeituanCoupon($post);
            }

            if ($result['success']) {
                apijson(0, $result['data']);
            } else {
                apijson(1, [], $result['message']);
            }
        } catch (\Exception $e) {
            apijson(1, [], '查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 查询抖音团购券信息
     * @param array $params 请求参数
     * @return array 查询结果
     */
    private function queryDouyinCoupon($params)
    {
        // 获取商户的抖音配置信息
        $config = \app\model\ThirdPartyConfig::where([
            'merchant_id' => $params['merchant_id'],
            'platform' => 'douyin'
        ])->find();

        if (!$config) {
            return ['success' => false, 'message' => '未配置抖音团购券接口信息'];
        }

        // 构建请求参数
        $requestData = [
            'app_id' => $config['app_id'],
            'code' => $params['code'],
            'timestamp' => time(),
            'nonce_str' => $this->generateNonceStr()
        ];

        // 生成签名
        $requestData['sign'] = $this->generateDouyinSign($requestData, $config['app_secret']);

        // 发送请求到抖音查询接口
        $url = 'https://open.douyin.com/api/trade/voucher/query';
        $response = \fast\Http::sendRequest($url, $requestData, 'POST', [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);

        if ($response['ret']) {
            $result = json_decode($response['msg'], true);

            if (isset($result['err_no']) && $result['err_no'] == 0) {
                // 查询成功
                $couponData = $result['data'];
                return [
                    'success' => true,
                    'data' => [
                        'platform' => 'douyin',
                        'platform_text' => '抖音',
                        'code' => $params['code'],
                        'name' => $couponData['title'] ?? '抖音团购券',
                        'amount' => $couponData['amount'] ?? 0,
                        'order_id' => $couponData['order_id'] ?? '',
                        'status' => $couponData['status'] ?? 0,
                        'status_text' => $this->getDouyinStatusText($couponData['status'] ?? 0),
                        'start_time' => $couponData['valid_start_time'] ?? 0,
                        'start_time_text' => isset($couponData['valid_start_time']) ? date('Y-m-d H:i:s', $couponData['valid_start_time']) : '',
                        'end_time' => $couponData['valid_end_time'] ?? 0,
                        'end_time_text' => isset($couponData['valid_end_time']) ? date('Y-m-d H:i:s', $couponData['valid_end_time']) : '',
                        'is_expired' => isset($couponData['valid_end_time']) && $couponData['valid_end_time'] < time() ? 1 : 0,
                        'raw_data' => $couponData
                    ]
                ];
            } else {
                // 查询失败
                return [
                    'success' => false,
                    'message' => $result['err_tips'] ?? '抖音查询失败'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => '抖音接口请求失败'
            ];
        }
    }

    /**
     * 查询美团团购券信息
     * @param array $params 请求参数
     * @return array 查询结果
     */
    private function queryMeituanCoupon($params)
    {
        // 获取商户的美团配置信息
        $config = \app\model\ThirdPartyConfig::where([
            'merchant_id' => $params['merchant_id'],
            'platform' => 'meituan'
        ])->find();

        if (!$config) {
            return ['success' => false, 'message' => '未配置美团团购券接口信息'];
        }

        // 构建请求参数
        $requestData = [
            'app_key' => $config['app_key'],
            'timestamp' => time(),
            'coupon_code' => $params['code'],
            'random' => mt_rand(100000, 999999)
        ];

        // 生成签名
        $requestData['sign'] = $this->generateMeituanSign($requestData, $config['app_secret']);

        // 发送请求到美团查询接口
        $url = 'https://openapi.meituan.com/api/deal/verification/query';
        $response = \fast\Http::sendRequest($url, $requestData, 'POST', [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);

        if ($response['ret']) {
            $result = json_decode($response['msg'], true);

            if (isset($result['status']) && $result['status'] == 0) {
                // 查询成功
                $couponData = $result['data'];
                return [
                    'success' => true,
                    'data' => [
                        'platform' => 'meituan',
                        'platform_text' => '美团',
                        'code' => $params['code'],
                        'name' => $couponData['deal_title'] ?? '美团团购券',
                        'amount' => $couponData['deal_price'] ?? 0,
                        'order_id' => $couponData['order_id'] ?? '',
                        'status' => $couponData['status'] ?? 0,
                        'status_text' => $this->getMeituanStatusText($couponData['status'] ?? 0),
                        'start_time' => $couponData['valid_start_time'] ?? 0,
                        'start_time_text' => isset($couponData['valid_start_time']) ? date('Y-m-d H:i:s', $couponData['valid_start_time']) : '',
                        'end_time' => $couponData['valid_end_time'] ?? 0,
                        'end_time_text' => isset($couponData['valid_end_time']) ? date('Y-m-d H:i:s', $couponData['valid_end_time']) : '',
                        'is_expired' => isset($couponData['valid_end_time']) && $couponData['valid_end_time'] < time() ? 1 : 0,
                        'verify_status' => $couponData['verify_status'] ?? 0,
                        'verify_status_text' => $this->getMeituanVerifyStatusText($couponData['verify_status'] ?? 0),
                        'verify_time' => $couponData['verify_time'] ?? 0,
                        'verify_time_text' => isset($couponData['verify_time']) ? date('Y-m-d H:i:s', $couponData['verify_time']) : '',
                        'raw_data' => $couponData
                    ]
                ];
            } else {
                // 查询失败
                return [
                    'success' => false,
                    'message' => $result['message'] ?? '美团查询失败'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => '美团接口请求失败'
            ];
        }
    }

    /**
     * 获取抖音状态文本
     * @param int $status 状态码
     * @return string 状态文本
     */
    private function getDouyinStatusText($status)
    {
        $statusMap = [
            0 => '未核销',
            1 => '已核销',
            2 => '已过期',
            3 => '已退款',
            4 => '已作废'
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取美团状态文本
     * @param int $status 状态码
     * @return string 状态文本
     */
    private function getMeituanStatusText($status)
    {
        $statusMap = [
            0 => '有效',
            1 => '已使用',
            2 => '已过期',
            3 => '已退款',
            4 => '已作废'
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取美团核销状态文本
     * @param int $status 状态码
     * @return string 状态文本
     */
    private function getMeituanVerifyStatusText($status)
    {
        $statusMap = [
            0 => '未核销',
            1 => '已核销',
            2 => '已冻结'
        ];

        return $statusMap[$status] ?? '未知状态';
    }

}
