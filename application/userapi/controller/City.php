<?php

namespace app\userapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\model\SystemCity as cityModel;
use think\Db;

/**
 * 省市区三级联动
 */
class City extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    protected $cityModel = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->cityModel = new cityModel();
    }


    /**
     * 获取所有省份
     */
    public function getProvinces()
    {
        $provinces = $this->cityModel->where(['level' => 0])->field('city_id as value, name')->select();
        apijson(0, $provinces, '获取成功');
    }

    /**
     * 获取指定省份下的城市
     */
    public function getCities()
    {
        $post = $this->request->post();
        if (empty($post['province_id'])) {
            apijson(1, [], '缺少省份ID');
        }

        $cities = $this->cityModel->where(['level' => 1, 'parent_id' => $post['province_id']])->field('city_id as value, name')->select();
        apijson(0, $cities, '获取成功');
    }

    /**
     * 获取指定城市下的区县
     */
    public function getDistricts()
    {
        $post = $this->request->post();
        if (empty($post['city_id'])) {
            apijson(1, [], '缺少城市ID');
        }

        $districts = $this->cityModel->where(['level' => 2, 'parent_id' => $post['city_id']])->field('city_id as value, name')->select();
        apijson(0, $districts, '获取成功');
    }

    /**
     * 获取完整的省市区数据
     */
    public function getAll()
    {
        $provinces = $this->cityModel->where(['level' => 0])->field('city_id, name')->select();

        foreach ($provinces as &$province) {
            $cities = $this->cityModel->where(['level' => 1, 'parent_id' => $province['city_id']])->field('city_id, name')->select();

            foreach ($cities as &$city) {
                $districts = $this->cityModel->where(['level' => 2, 'parent_id' => $city['city_id']])->field('city_id, name')->select();
                $city['districts'] = $districts;
            }

            $province['cities'] = $cities;
        }

        apijson(0, $provinces, '获取成功');
    }


}
