<?php

namespace app\userapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\model\Evaluate as evaluateModel;

/**
 * 评价
 */
class Evaluate extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    protected $evaluateModel;

    public function _initialize()
    {
        parent::_initialize();

        $this->evaluateModel = new evaluateModel();
    }

    /**
     * 评价列表
     *
     */
    public function getEvaluateList()
    {
        $merchant_id = $this->request->post('merchant_id');
        $store_id = $this->request->post('store_id');
        $is_type = $this->request->post('is_type');
        $hair_id = $this->request->post('hair_id') ?? null;

        if (!$merchant_id) {
            apijson(1, [], '缺少商户id');
        }
        if ($is_type == 2 && !$store_id) {
            apijson(1, [], '缺少门店id');
        }
        if (!$is_type) {
            apijson(1, [], '缺少评价类型');
        }

        $where = [
            'merchant_id' => $merchant_id,
            'is_type' => $is_type,
            'is_del' => 1,
            'status' => 1
        ];
        if( $is_type == 2 && $store_id ){
            $where['store_id'] = $store_id;
        }

        // 如果指定了理发师ID，则添加到查询条件
        if (!empty($hair_id)) {
            $where['hair_id'] = $hair_id;
        }

        try {
            // 查询评价列表
            $list = $this->evaluateModel
                ->where($where)
                ->order('create_time', 'desc')
                ->limit($this->page, $this->limit)
                ->select();
            // 处理评价数据
            foreach ($list as &$item) {
                // 添加用户信息
                $user = \think\Db::name('user')->where('id', $item['user_id'])->find();
                $item['user_info'] = [
                    'nickname' => $user ? $user['nickname'] : '',
                    'avatar' => $user ? cdnurl($user['avatar'], true) : ''
                ];

                // 处理图片和标签
                $item['images_list'] = !empty($item['images']) ? explode(',', $item['images']) : [];
                $item['labels_list'] = !empty($item['labels']) ? explode(',', $item['labels']) : [];

                // 处理追加评价
                if ($item['is_append'] == 1) {
                    $item['append_images_list'] = !empty($item['append_images']) ? explode(',', $item['append_images']) : [];
                }

                // 根据评价类型添加不同的关联信息
                if ($is_type == 1) { // 商品评价
                    // 添加商品信息
                    if ($item['goods_id']) {
                        $goods = \think\Db::name('delivery_goods')->where('id', $item['goods_id'])->find();
                        $item['goods_info'] = [
                            'id' => $goods ? $goods['id'] : 0,
                            'name' => $goods ? $goods['name'] : '',
                            'image' => $goods ? cdnurl($goods['cimage'], true) : '',
                            'price' => $goods ? $goods['price'] : 0
                        ];
                    } else {
                        $item['goods_info'] = null;
                    }
                } else if ($is_type == 2) { // 理发评价
                    // 添加理发师信息
                    if ($item['hair_id']) {
                        $hair = \think\Db::name('user')->where('id', $item['hair_id'])->find();
                        $item['hair_info'] = [
                            'id' => $hair ? $hair['id'] : 0,
                            'name' => $hair ? $hair['nickname'] : '',
                            'avatar' => $hair ? cdnurl($hair['avatar'], true) : '',
                            'level' => $hair ? $hair['level'] : ''
                        ];
                    } else {
                        $item['hair_info'] = null;
                    }
                }

                // 格式化时间
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                if ($item['is_append'] == 1 && $item['append_time']) {
                    $item['append_time_text'] = date('Y-m-d H:i:s', $item['append_time']);
                }

                // 添加商家回复信息
                if ($item['is_reply'] == 1) {
                    $item['reply_time_text'] = date('Y-m-d H:i:s', $item['reply_time']);
                }
            }

            apijson(0, $list);
        } catch (UploadException $e) {
            apijson(1, [], '获取评价列表失败: ' . $e->getMessage());
        }
    }
    /**
     * 单个商品评价列表
     *
     */
    public function getGoodsEvaluateList()
    {
        $merchant_id = $this->request->post('merchant_id');
        $goods_id = $this->request->post('goods_id');
        $hair_id = $this->request->post('hair_id') ?? null;

        if (!$merchant_id) {
            apijson(1, [], '缺少商户id');
        }
        $where = [
            'merchant_id' => $merchant_id,
            'goods_id' => $goods_id,
            'is_del' => 1,
            'status' => 1
        ];

        try {
            // 查询评价列表
            $list = $this->evaluateModel
                ->where($where)
                ->order('create_time', 'desc')
                ->limit($this->page, $this->limit)
                ->select();
            // 处理评价数据
            foreach ($list as &$item) {
                // 添加用户信息
                $user = \think\Db::name('user')->where('id', $item['user_id'])->find();
                $item['user_info'] = [
                    'nickname' => $user ? $user['nickname'] : '',
                    'avatar' => $user ? cdnurl($user['avatar'], true) : ''
                ];

                // 处理图片和标签
                $item['images_list'] = !empty($item['images']) ? explode(',', $item['images']) : [];
                $item['labels_list'] = !empty($item['labels']) ? explode(',', $item['labels']) : [];

                // 处理追加评价
                if ($item['is_append'] == 1) {
                    $item['append_images_list'] = !empty($item['append_images']) ? explode(',', $item['append_images']) : [];
                }
                 // 商品评价
                // 添加商品信息
                if ($item['goods_id']) {
                    $goods = \think\Db::name('delivery_goods')->where('id', $item['goods_id'])->find();
                    $item['goods_info'] = [
                        'id' => $goods ? $goods['id'] : 0,
                        'name' => $goods ? $goods['name'] : '',
                        'image' => $goods ? cdnurl($goods['cimage'], true) : '',
                        'price' => $goods ? $goods['price'] : 0
                    ];
                } else {
                    $item['goods_info'] = null;
                }

                // 格式化时间
                $item['create_time_text'] = date('Y-m-d H:i:s', $item['create_time']);
                if ($item['is_append'] == 1 && $item['append_time']) {
                    $item['append_time_text'] = date('Y-m-d H:i:s', $item['append_time']);
                }

                // 添加商家回复信息
                if ($item['is_reply'] == 1) {
                    $item['reply_time_text'] = date('Y-m-d H:i:s', $item['reply_time']);
                }
            }

            apijson(0, $list);
        } catch (UploadException $e) {
            apijson(1, [], '获取评价列表失败: ' . $e->getMessage());
        }
    }


    /**
     * 添加评价
     * @ApiTitle    (添加评价)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=true, description="门店ID")
     * @ApiParams   (name="user_id", type="integer", required=true, description="用户ID")
     * @ApiParams   (name="hair_id", type="integer", required=false, description="理发师ID")
     * @ApiParams   (name="goods_id", type="integer", required=false, description="商品ID")
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiParams   (name="content", type="string", required=true, description="评价内容")
     * @ApiParams   (name="labels", type="string", required=false, description="标签，多个用逗号分隔")
     * @ApiParams   (name="images", type="string", required=false, description="图片地址，多个用逗号分隔")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function labelAdd()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必要参数
        $requiredFields = ['merchant_id', 'is_type', 'user_id', 'order_id', 'content'];
        foreach ($requiredFields as $field) {
            if (empty($post[$field])) {
                apijson(1, [], "缺少必要参数: {$field}");
            }
        }
        // 如果是理发订单 必须要有门店ID
        if( $post['is_type'] == 2 && empty($post['store_id']) ){
            apijson(1, [], '理发订单必须要有门店ID');
        }

        // 验证订单是否已评价
        $evaluateModel = new \app\model\Evaluate();
        $exists = $evaluateModel->where([
            'order_id' => $post['order_id'],
            'is_del' => 1
        ])->find();

        if ($exists) {
            apijson(1, [], '该订单已评价');
        }
//        $labels = implode(',',$post['labels']);
//        $images = implode(',',$post['images']);
        // 计算评分  满意5分 一般3分 不满意1分
        if( $post['satisfied_type'] == 1 ){
            $score = 5;
            $satisfied = 100;
        }else if( $post['satisfied_type'] == 2 ){
            $score = 3;
            $satisfied = 80;
        }else if( $post['satisfied_type'] == 3 ){
            $score = 1;
            $satisfied = 60;
        }
        // 开始事务
        \think\Db::startTrans();

        try {
            // 构建评价数据
            $evaluateData = [
                'merchant_id' => $post['merchant_id'],
                'store_id' => $post['store_id'] ?? '',
                'user_id' => $post['user_id'],
                'hair_id' => $post['hair_id'] ?? 0,
                'goods_id' => $post['goods_id'] ?? 0,
                'order_id' => $post['order_id'],
                'satisfied_type' => $post['satisfied_type'],
                'score' => $score,
                'satisfied' => $satisfied,
                'content' => $post['content'],
                'labels' => $post['labels'] ?? '', // 直接存储标签字符串
                'images' => $post['images'] ?? '', // 直接存储图片地址字符串
                'status' => 1, // 默认状态为已审核
                'create_time' => time(),
                'is_type' => $post['is_type'],
                'is_del' => 1
            ];

            // 保存评价
            $result = $evaluateModel->allowField(true)->save($evaluateData);
            $evaluate_id = $evaluateModel->getLastInsID();
            if (!$result) {
                apijson(1, [], '评价保存失败');
            }
            if( $post['is_type'] == 1 ) {
                // 更新订单评价状态
                \think\Db::name('delivery_order')->where('id', $post['order_id'])->update([
                    'is_evaluate' => 1,
                    'evaluate_time' => time()
                ]);
            }else{
                // 更新理发订单评价状态
                \think\Db::name('delivery_order_hairdressing')->where('id', $post['order_id'])->update([
                    'is_evaluate' => 1,
                    'evaluate_time' => time()
                ]);
            }

            // 提交事务
            \think\Db::commit();

            // 获取完整的评价信息
            $evaluate = $evaluateModel->where('id', $evaluate_id)->find();

            apijson(0, $evaluate, '评价成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '评价失败: ' . $e->getMessage());
        }
    }

    /**
     * 追加评价
     * @ApiTitle    (追加评价)
     * @ApiMethod   (POST)
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiParams   (name="content", type="string", required=true, description="追加评价内容")
     * @ApiParams   (name="images", type="string", required=false, description="追加图片地址，多个用逗号分隔")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function appendEvaluate()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必要参数
        if (empty($post['order_id']) || empty($post['content'])) {
            apijson(1, [], '缺少必要参数');
        }

        // 查找原评价
        $evaluateModel = new \app\model\Evaluate();
        $evaluate = $evaluateModel->where([
            'order_id' => $post['order_id'],
            'is_del' => 1
        ])->find();

        if (!$evaluate) {
            apijson(1, [], '未找到原评价，请先评价');
        }

        // 检查是否已追加评价
        if ($evaluate['is_append'] == 1) {
            apijson(1, [], '该订单已追加过评价，不能重复追加');
        }

        // 开始事务
        \think\Db::startTrans();

        try {
            // 更新原评价，标记为已追加
            $updateData = [
                'is_append' => 1,
                'append_time' => time(),
                'append_content' => $post['content'],
                'append_images' => $post['images'] ?? '' // 直接存储追加图片地址字符串
            ];

            $result = $evaluateModel->where('id', $evaluate['id'])->update($updateData);

            if (!$result) {
                throw new \Exception('追加评价失败');
            }

            // 提交事务
            \think\Db::commit();

            // 获取更新后的评价信息
            $updatedEvaluate = $evaluateModel->where('id', $evaluate['id'])->find();

            apijson(0, $updatedEvaluate, '追加评价成功');
        } catch (UploadException $e) {
            // 回滚事务
            \think\Db::rollback();
            apijson(1, [], '追加评价失败: ' . $e->getMessage());
        }
    }



}
