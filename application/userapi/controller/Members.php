<?php

namespace app\userapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\library\Token;
use fast\Random;
use think\Config;
use think\Response;
use think\Validate;
use app\model\user\Members as UserModel;
use app\common\controller\WxMiniProgramService;

/**
 * 会员卡管理  接口
 */
class Members extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = [];
    protected $model = null;


    public function _initialize()
    {
        parent::_initialize();
        $this->model = new UserModel();

    }

    /**
     * 会员卡信息
     */
    public function memberInfo()
    {
        $post = $this->request->post();

        if (empty($post['merchant_id'])) {
            apijson(1, [], '缺少参数');
        }

        try {
            $where = ['merchant_id' => $post['merchant_id']];

            $memberInfo = $this->model->where($where)->find();

            if (empty($memberInfo)) {
                apijson(1, [], '未找到会员卡信息');
            }


            apijson(0, $memberInfo, '获取成功');
        } catch (UploadException $e) {
            apijson(1, [], $e->getMessage());
        }
    }

}
