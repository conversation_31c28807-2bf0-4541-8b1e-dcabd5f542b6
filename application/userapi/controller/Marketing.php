<?php

namespace app\userapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use app\model\marketing\Points as pointsModel;

/**
 * 积分管理
 */
class Marketing extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    protected $pointsModel;

    public function _initialize()
    {
        parent::_initialize();
        $this->pointsModel = new pointsModel();
    }

    /**
     * 获取积分任务列表
     */
    public function getPointsList()
    {
        $post = $this->request->post();
        if (empty($post['merchant_id'])) {
            apijson(1, [], '缺少商户id');
        }

        try {
            $list = $this->pointsModel->where([
                'merchant_id' => $post['merchant_id'],
                'status' => 1 // 假设只获取启用状态的积分任务
            ])->select();

            apijson(0, $list);
        } catch (UploadException $e) {
            apijson(1,[], '操作失败');
        }
    }




}
