<?php

namespace app\userapi\controller;

use app\common\controller\Api;
use app\common\exception\UploadException;
use addons\delivery\model\Goods as GoodsModel;
use app\userapi\controller\Member as memberController;
use think\Db;
use app\model\delivery\Guarantee as guaranteeModel;
use addons\delivery\model\FreeDeliveryRules as FreeDeliveryRulesModel;
use app\model\delivery\send\Address as addressModel;
use app\model\Evaluate as EvaluateModel;

use addons\delivery\model\Order as orderModel;
use addons\delivery\model\OrderDetail as orderDetailModel;
use addons\delivery\model\PostageRules as postageRulesModel;
use app\model\user\Address as userAddressModel;
use app\admin\model\delivery\Refund as refundModel;

/**
 * 商城管理
 */
class Goods extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    protected $GoodsModel = null;
    protected $guaranteeModel = null;
    protected $FreeDeliveryRulesModel = null;
    protected $EvaluateModel = null;
    protected $addressModel = null;
    protected $orderModel = null;
    protected $orderDetailModel = null;
    protected $postageRulesModel = null;
    protected $userAddressModel = null;
    protected $memberController = null;
    protected $refundModel = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->GoodsModel = new GoodsModel();
        $this->guaranteeModel = new guaranteeModel();
        $this->FreeDeliveryRulesModel = new FreeDeliveryRulesModel();
        $this->EvaluateModel = new EvaluateModel();
        $this->addressModel = new addressModel();
        $this->orderModel = new orderModel();
        $this->orderDetailModel = new orderDetailModel();
        $this->postageRulesModel = new postageRulesModel();
        $this->userAddressModel = new userAddressModel();
        $this->memberController = new memberController();
        $this->refundModel = new refundModel();
    }

    /**
     * 商品列表
     *
     */
    public function getGoodsList()
    {
        if( !$this->request->isPost() ){
            apijson(1,[],'非法请求');
        }
        $post = $this->request->post();
        if( empty( $post['merchant_id'] ) || empty( $post['is_points'] ) ){
            apijson(1,[],'缺少参数');
        }
        try {
            $where['merchant_id'] = $post['merchant_id'];
            $where['is_points'] = $post['is_points'];
            $list = $this->GoodsModel->where($where)->limit($this->page,$this->limit)->select();
            apijson(0,$list);
        } catch ( UploadException $e ){
            $this->error($e->getMessage());
        }

    }

    // 商品详情
    public function getGoodsDetails()
    {
        $goods_id = $this->request->post('goods_id');
        if( !$goods_id ){
            apijson(1,'缺少商品ID');
        }
        try {
            $details = $this->GoodsModel->where('id',$goods_id)->find();
            apijson(0,$details);
        } catch (UploadException $e){
            $this->error($e->getMessage());
        }
    }

    /**
     * 商户商城获取商品
     * @ApiTitle    (商户商城获取商品)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="store_id", type="integer", required=false, description="门店ID")
     * @ApiParams   (name="keyword", type="string", required=false, description="搜索关键词")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function getMerchantGoods()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();
        if (empty($post['merchant_id'])) {
            apijson(1, [], '缺少商户ID');
        }

        try {
            // 构建查询条件
            $where = [
                'merchant_id' => $post['merchant_id'],
                'is_points' => 2,
                'is_list' => 1
            ];

            // 如果有门店ID，则筛选该门店可用的商品
            if (!empty($post['store_id'])) {
                $where['store_ids'] = ['like', '%,' . $post['store_id'] . ',%'];
            }

            // 如果有搜索关键词，则添加搜索条件
            if (!empty($post['name'])) {
                $where['name'] = ['like', '%' . $post['name'] . '%'];
            }

            // 展示区
            if (!empty($post['area_id'])) {
                $where['area_id'] = $post['area_id'];
            }

            // 查询商品数据
            $goodsList = $this->GoodsModel
                ->where($where)
                ->order('id DESC')
                ->limit($this->page, $this->limit)
                ->select();
            apijson(0, $goodsList);
        } catch (UploadException $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取商品展示区列表
     * @ApiTitle    (获取商品展示区列表)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function getAreaList()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();
        if (empty($post['merchant_id'])) {
            apijson(1, [], '缺少商户ID');
        }

        try {
            // 查询商品展示区
            $areaList = Db::name('delivery_area')
                ->where('merchant_id', $post['merchant_id'])
                ->field('value as id, name')
                ->order('id ASC')
                ->select();

            apijson(0, $areaList);
        } catch (UploadException $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 商品详情
     */
    public function details()
    {
        $post = $this->request->post();
        if( empty($post['goods_id']) ){
            apijson(1,[],'缺少商品ID');
        }
        try {
            $list = $this->GoodsModel->where([
                'id' => $post['goods_id']
            ])->find();
            // 获取保障信息
            $list['guarantee'] = $this->guaranteeModel
                ->where('status',1)
                ->find();
            // 查询包邮规则
            $freeDeliveryRules = $this->FreeDeliveryRulesModel
                ->where('is_show',1)
                ->find();
            $list['free_delivery_rules'] = $freeDeliveryRules;
            // 查询配送信息
            $list['delivery_info'] = $this->addressModel
                ->where('merchant_id',$list['merchant_id'])
                ->where('status',1)
                ->find();
            // 商品评价总数
            $list['evaluate_count'] = $this->EvaluateModel
                ->where('goods_id',$post['goods_id'])
                ->where('is_del',1)
                ->where('status',1)
                ->count();
            apijson(0,$list);
        } catch (UploadException $e) {
            $this->error($e->getMessage());
        }
    }


    /**
     * 商品下单 -- 创建待付款订单
     * @ApiTitle    (创建待付款订单)
     * @ApiMethod   (POST)
     * @ApiParams   (name="merchant_id", type="integer", required=true, description="商户ID")
     * @ApiParams   (name="user_id", type="integer", required=true, description="用户ID")
     * @ApiParams   (name="goods_id", type="array", required=true, description="商品ID数组")
     * @ApiParams   (name="goods_num", type="array", required=true, description="商品数量数组")
     * @ApiParams   (name="address_id", type="integer", required=true, description="收货地址ID")
     * @ApiParams   (name="remark", type="string", required=false, description="订单备注")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function createGoodsOrder()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必要参数
        $requiredFields = ['merchant_id', 'user_id', 'goods_id', 'goods_num', 'address_id'];
        foreach ($requiredFields as $field) {
            if (!isset($post[$field]) || $post[$field] === '') {
                apijson(1, [], "缺少必要参数: {$field}");
            }
        }

        // 开始事务
        Db::startTrans();
        try {
            // 生成订单号
            $orderNo = date("YmdHis") . mt_rand(1000, 9999);

            // 计算商品总价和准备订单详情数据
            $goodsId = $post['goods_id'];
            $goodsNum = $post['goods_num'];
            $totalPrice = 0;
            $goodsPrice = 0;
            $expressPrice = 0;
            $orderDetails = [];

            // 查询商品信息
            $goods = $this->GoodsModel->where('id', $goodsId)->find();
            if (!$goods) {
                Db::rollback();
                apijson(1, [], "商品ID: {$goodsId} 不存在");
            }

            $num = $goodsNum;
            $price = $post['price'];
            $itemTotal = $price * $num;
            $goodsPrice += $itemTotal;

            $orderDetails[] = [
                'goods_id' => $goodsId,
                'num' => $num,
                'price' => $price,
                'name' => $goods['name'],
                'cimage' => $goods['cimage'],
                'total_price' => $itemTotal,
                'freight' => $goods['freight'],
                'weight' => $goods['weight'],
            ];

            // 计算运费 (可根据实际需求调整)
            $expressPrice = $this->calculateExpressPrice($post,$goods);

            $totalPrice = $goodsPrice + $expressPrice;

            // 获取收货地址信息
            $userAddressInfo = $this->userAddressModel->where('id',$post['address_id'])->find();
            $addressArr = explode('/', $userAddressInfo['city']);


            // 创建订单主表数据
            $orderData = [
                'merchant_id' => $post['merchant_id'],
                'user_id' => $post['user_id'],
                'order_no' => $orderNo,
                'goods_price' => $goodsPrice,
                'express_price' => $expressPrice,
                'total_price' => $totalPrice,
                'address_id' => $post['address_id'],
                'accept_mobile' => $userAddressInfo['mobile'],
                'accept_name' => $userAddressInfo['name'],
                'accept_province' => $addressArr[0] ?? '',
                'accept_city' => $addressArr[1] ?? '',
                'accept_exp_area' => $addressArr[2] ?? '',
                'accept_address' => $userAddressInfo['address'],
                'remark' => $post['remark'] ?? '',
                'is_pay' => 0, // 未支付
                'create_time' => time(),
                'status' => 1, // 订单状态：待付款
            ];
            // 保存订单
            $result = $this->orderModel->allowField(true)->save($orderData);
            if (!$result) {
                Db::rollback();
                apijson(1, [], '创建订单失败');
            }

            // 获取新创建的订单ID
            $orderId = $this->orderModel->getLastInsID();

            // 保存订单详情
            foreach ($orderDetails as &$detail) {
                $detail['order_id'] = $orderId;
            }
            $detailResult = $this->orderDetailModel->insertAll($orderDetails);
            if (!$detailResult) {
                Db::rollback();
                apijson(1, [], '创建订单详情失败');
            }

            // 提交事务
            Db::commit();

            // 查询创建的订单详情
            $order = $this->orderModel->with(['orderDetail'])->where('id', $orderId)->find();

            apijson(0, $order, '创建订单成功');

        } catch (UploadException $e) {
            // 回滚事务
            Db::rollback();
            apijson(1, [], '创建订单失败: ' . $e->getMessage());
        }
    }


    // 创建商品订单中 调用 计算运费
    public function calculateExpressPrice( $data,$goods )
    {
        // 获取运费规则
        $rules = $this->postageRulesModel->where('is_default',1)->find();
        if( empty( $rules ) ){
            apijson(1, [], '运费规则不存在');
        }
        $rulesDetail = html_entity_decode($rules['detail']);
        $rulesDetailArr = json_decode($rulesDetail, true);

        // 获取地址信息
        $addressInfo = $this->addressModel->where('id',$data['address_id'])->find();
        $cityArr = explode('/', $addressInfo['city']);
        $city = $cityArr[1];
        // 获取包邮规则
        $freeDeliveryRules = $this->FreeDeliveryRulesModel
            ->where('is_show',1)
            ->find();
        $cArr = html_entity_decode($freeDeliveryRules['city_name']);
        if( strpos($cArr, $city) !== false ){
            $expressPrice = 0;
        }else {

            // 按件计费
            if ($rules['type'] == 1) {
                $expressPrice = round($data['num'] * $rulesDetailArr[0]['frist_price'], 2);
            } else {
                $expressPrice = round($goods['weight'] * $rulesDetailArr[0]['frist_price'] * $data['num'], 2);
            }
        }
        return $expressPrice;


    }


    // 更换地址 调用 计算运费
    public function getCalculateExpressPrice()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必要参数
        $requiredFields = ['merchant_id', 'goods_list','address_id','num'];
        foreach ($requiredFields as $field) {
            if (!isset($post[$field]) || $post[$field] === '') {
                apijson(1, [], "缺少必要参数: {$field}");
            }
        }

        $cleanJson = html_entity_decode($post['goods_list']);
        $goods_list = json_decode($cleanJson, true);

        // 获取地址信息
        $addressInfo = $this->addressModel->where('id',$post['address_id'])->find();
        $cityArr = explode('/', $addressInfo['city']);
        $city = $cityArr[1];

        // 获取运费规则
        $rules = $this->postageRulesModel->where('is_default',1)->find();
        $rulesDetail = html_entity_decode($rules['detail']);
        $rulesDetailArr = json_decode($rulesDetail, true);
        if( empty( $rules ) ){
            apijson(1, [], '运费规则不存在');
        }
        // 获取包邮规则
        $freeDeliveryRules = $this->FreeDeliveryRulesModel
            ->where('is_show',1)
            ->find();
        $cArr = html_entity_decode($freeDeliveryRules['city_name']);
        $expressPrice = 0;

        if( strpos($cArr, $city) !== false ){
            $expressPrice = 0;
        }else{

            foreach ( $goods_list as &$goods ) {
                $Price = 0;
                // 获取商品信息
                $goodsInfo = $this->GoodsModel->where('id',$goods['goods_id'])->find();
                // 按件计费
                if( $rules['type'] == 1 ){
                    $Price = round($post['num']*$rulesDetailArr[0]['frist_price'],2);
                }else{
                    $Price = round($goodsInfo['weight']*$rulesDetailArr[0]['frist_price']*$post['num'],2);
                }
                $expressPrice += $Price;
            }

        }
        $data = [
            'expressPrice' => $expressPrice
        ];
        apijson(0, $data, '操作成功');


    }

    /**
     * 取消商品订单
     * @ApiTitle    (取消商品订单)
     * @ApiMethod   (POST)
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiParams   (name="reason", type="string", required=true, description="取消原因")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function cancelOrder()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $order_id = $this->request->post('order_id');
        $reason = $this->request->post('reason');

        if (!$order_id) {
            apijson(1, [], '缺少订单ID');
        }

        if (!$reason) {
            apijson(1, [], '请填写取消原因');
        }

        // 开始事务
        Db::startTrans();
        try {
            // 查询订单信息
            $order = $this->orderModel->where('id', $order_id)->find();
            if (!$order) {
                apijson(1, [], '订单不存在');
            }

            // 检查订单状态，只有待付款的订单可以取消
            if ($order['is_pay'] != 0 && $order['status'] != 1) {
                apijson(1, [], '只有待付款的订单可以取消');
            }

            // 如果订单已支付，需要进行退款处理
            if ($order['is_pay'] == 1) {
                // 退款逻辑，根据实际支付方式处理
                // 这里可以参考 application/userapi/controller/order/Hairdressing.php 中的退款逻辑
            }

            // 更新订单状态为已取消
            $result = $this->orderModel->where('id', $order_id)->update([
                'status' => 4, // 假设4表示已取消
                'is_pay' => 2, // 假设2表示已取消支付
                'reason' => $reason,
                'cancel_time' => date('Y-m-d H:i:s',time())
            ]);

            if (!$result) {
                Db::rollback();
                apijson(1, [], '取消订单失败');
            }

            // 提交事务
            Db::commit();

            apijson(0, [], '取消订单成功');
        } catch (UploadException $e) {
            // 回滚事务
            Db::rollback();
            apijson(1, [], '取消订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 商品订单支付
     * @ApiTitle    (商品订单支付)
     * @ApiMethod   (POST)
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiParams   (name="pay_type", type="integer", required=true, description="支付方式：1=微信支付，2=支付宝支付")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function payOrder()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $order_id = $this->request->post('order_id');
        $pay_type = $this->request->post('pay_type');
        $wx_openid = $this->request->post('wx_openid');

        if (!$order_id) {
            apijson(1, [], '缺少订单ID');
        }

        if (!in_array($pay_type, [1, 2])) {
            apijson(1, [], '支付方式不正确');
        }

        try {
            // 查询订单信息
            $order = $this->orderModel->where('id', $order_id)->find();
            if (!$order) {
                apijson(1, [], '订单不存在');
            }

            // 检查订单状态，只有待付款的订单可以支付
            if ($order['is_pay'] == 0 && $order['status'] != 1) {
                apijson(1, [], '该订单状态不允许支付');
            }

            // 检查订单是否已支付
            if ($order['is_pay'] == 1) {
                apijson(1, [], '该订单已支付');
            }

            // 支付参数

            // 测试支付
            if( $pay_type == 1 ){// 微信支付
                $type = 'T_MINIAPP';
            }
            // 调用支付接口，获取支付参数
            // 这里需要根据实际的支付系统进行调整
            $req_date = date("Ymd");
            $notify_url = "https://youjian.hrbyc.top/ystgjXhKQw.php/userapi/member/payNotify";
            $payResult = $this->memberController->createWxpay($order['order_no'],$req_date, $order['total_price'],$type, '商品订单',$wx_openid,$notify_url);

            // 返回支付参数给前端
            apijson(0, $payResult, '获取支付参数成功');
        } catch (UploadException $e) {
            apijson(1, [], '支付失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认收货
     * @ApiTitle    (确认收货)
     * @ApiMethod   (POST)
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function confirmReceipt()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $order_id = $this->request->post('order_id');

        if (!$order_id) {
            apijson(1, [], '缺少订单ID');
        }

        // 开始事务
        Db::startTrans();
        try {
            // 查询订单信息
            $order = $this->orderModel->where('id', $order_id)->find();
            if (!$order) {
                apijson(1, [], '订单不存在');
            }

            // 检查订单状态，只有已发货的订单可以确认收货
            if ($order['status'] != 1) { // 假设3表示已发货
                apijson(1, [], '只有已发货的订单可以确认收货');
            }

            // 更新订单状态为已完成
            $result = $this->orderModel->where('id', $order_id)->update([
                'status' => 5, // 假设5表示已完成
                'complete_time' => time(),
                'is_complete' => 1 // 标记为已完成
            ]);

            if (!$result) {
                Db::rollback();
                apijson(1, [], '确认收货失败');
            }

            // 提交事务
            Db::commit();

            // 可以在这里添加其他业务逻辑，如：
            // 1. 给商家发送通知
            // 2. 更新商品销量
            // 3. 生成评价记录
            // 4. 处理积分或优惠券奖励

            apijson(0, [], '确认收货成功');
        } catch (UploadException $e) {
            // 回滚事务
            Db::rollback();
            apijson(1, [], '确认收货失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取订单详情
     * @ApiTitle    (获取订单详情)
     * @ApiMethod   (POST)
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function getOrderDetail()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $order_id = $this->request->post('order_id');

        if (!$order_id) {
            apijson(1, [], '缺少订单ID');
        }

        try {
            // 查询订单详情
            $order = $this->orderModel
                ->with(['orderDetail'])
                ->where('id', $order_id)
                ->find();

            if (!$order) {
                apijson(1, [], '订单不存在');
            }

            // 添加状态文本
            switch ($order['status']) {
                case 1:
                    $order['status_text'] = '待付款';
                    break;
                case 2:
                    $order['status_text'] = '待发货';
                    break;
                case 3:
                    $order['status_text'] = '待收货';
                    break;
                case 4:
                    $order['status_text'] = '已取消';
                    break;
                case 5:
                    $order['status_text'] = '已完成';
                    break;
                default:
                    $order['status_text'] = '未知状态';
            }

            // 格式化时间
            if ($order['create_time']) {
                $order['create_time_text'] = date('Y-m-d H:i:s', $order['create_time']);
            }
            if ($order['pay_time']) {
                $order['pay_time_text'] = date('Y-m-d H:i:s', $order['pay_time']);
            }
            if ($order['complete_time']) {
                $order['complete_time_text'] = date('Y-m-d H:i:s', $order['complete_time']);
            }
            if ($order['cancel_time']) {
                $order['cancel_time_text'] = date('Y-m-d H:i:s', $order['cancel_time']);
            }

            // 获取收货地址信息
            if ($order['address_id']) {
                $address = $this->userAddressModel->where('id', $order['address_id'])->find();
                $order['address'] = $address;
            }

            apijson(0, $order);
        } catch (UploadException $e) {
            apijson(1, [], '获取订单详情失败: ' . $e->getMessage());
        }
    }


    /**
     * 申请退货退款
     * @ApiTitle    (申请退货退款)
     * @ApiMethod   (POST)
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiParams   (name="reason", type="string", required=true, description="退款原因")
     * @ApiParams   (name="refund_amount", type="float", required=true, description="退款金额")
     * @ApiParams   (name="images", type="string", required=false, description="凭证图片，多张用逗号分隔")
     * @ApiParams   (name="description", type="string", required=false, description="退款说明")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function applyRefund()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必填参数
        if (empty($post['order_id']) ||empty($post['type']) || empty($post['reason']) || !isset($post['refund_amount'])) {
            apijson(1, [], '缺少必要参数');
        }

        // 开始事务
        Db::startTrans();
        try {
            // 查询订单信息
            $order = $this->orderModel->where('id', $post['order_id'])->find();
            if (!$order) {
                apijson(1, [], '订单不存在');
            }
            // 检查订单状态，只有已付款的订单可以申请退款
            if ($order['is_pay'] != 1 || $order['status'] >= 2) {
                apijson(1, [], '当前订单状态不支持申请退款');
            }
            // 检查退款金额是否合理
            if ($post['refund_amount'] <= 0 || $post['refund_amount'] > $order['total_price']) {
                apijson(1, [], '退款金额不合理');
            }

            // 生成订单号
            $order_no = date("YmdHis").mt_rand();
            // 创建退款记录
            $refundData = [
                'order_id' => $post['order_id'],
                'refund_order_no' => $order_no,
                'user_id' => $order['user_id'],
                'merchant_id' => $order['merchant_id'],
                'refund_amount' => $post['refund_amount'],
                'reason' => $post['reason'],
                'image' => isset($post['image']) ? $post['image'] : '',
                'status' => 1, // 1表示申请中
                'create_time' => time(),
                'update_time' => time()
            ];

            $result = $this->refundModel->insert($refundData);
            if (!$result) {
                Db::rollback();
                apijson(1, [], '申请退款失败');
            }

            // 更新订单状态为退款中
            $this->orderModel->where('id', $post['order_id'])->update([
                'refund_status' => 1 // 1表示退款申请中
            ]);

            // 提交事务
            Db::commit();

            apijson(0, [], '申请退款成功，请等待商家审核');
        } catch (UploadException $e) {
            // 回滚事务
            Db::rollback();
            apijson(1, [], '申请退款失败: ' . $e->getMessage());
        }
    }

    /**
     * 查询退款进度
     * @ApiTitle    (查询退款进度)
     * @ApiMethod   (POST)
     * @ApiParams   (name="order_id", type="integer", required=true, description="订单ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function getRefundStatus()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $order_id = $this->request->post('order_id');

        if (!$order_id) {
            apijson(1, [], '缺少订单ID');
        }

        try {
            // 查询退款记录
            $refund = $this->refundModel->where('order_id', $order_id)->order('id', 'desc')->find();

            if (!$refund) {
                apijson(1, [], '未找到退款记录');
            }

            // 添加状态文本
            switch ($refund['status']) {
                case 1:
                    $refund['status_text'] = '申请中';
                    break;
                case 2:
                    $refund['status_text'] = '商家已同意';
                    break;
                case 3:
                    $refund['status_text'] = '退款成功';
                    break;
                case 4:
                    $refund['status_text'] = '商家已拒绝';
                    break;
                case 5:
                    $refund['status_text'] = '已取消';
                    break;
                default:
                    $refund['status_text'] = '未知状态';
            }

            // 格式化时间
            if ($refund['create_time']) {
                $refund['create_time_text'] = date('Y-m-d H:i:s', $refund['create_time']);
            }
            if ($refund['update_time']) {
                $refund['update_time_text'] = date('Y-m-d H:i:s', $refund['update_time']);
            }

            // 获取订单信息
            $order = $this->orderModel->where('id', $order_id)->find();
            $refund['order'] = $order;

            apijson(0, $refund, '获取退款进度成功');
        } catch (UploadException $e) {
            apijson(1, [], '获取退款进度失败: ' . $e->getMessage());
        }
    }

    // 多商品结算接口
    /**
     * 多商品结算接口
     * @ApiTitle    (多商品结算)
     * @ApiMethod   (POST)
     * @ApiParams   (name="goods_list", type="array", required=true, description="商品列表，包含商品ID和数量")
     * @ApiParams   (name="address_id", type="integer", required=true, description="收货地址ID")
     * @ApiParams   (name="coupon_id", type="integer", required=false, description="优惠券ID")
     * @ApiParams   (name="remark", type="string", required=false, description="订单备注")
     * @ApiReturnParams   (name="code", type="integer", required=true, description="状态码")
     * @ApiReturnParams   (name="msg", type="string", required=true, description="返回信息")
     * @ApiReturnParams   (name="data", type="object", required=true, description="返回数据")
     */
    public function multiGoodsCheckout()
    {
        if (!$this->request->isPost()) {
            apijson(1, [], '非法请求');
        }

        $post = $this->request->post();

        // 验证必要参数
        $requiredFields = ['merchant_id', 'user_id', 'goods_list', 'address_id'];
        foreach ($requiredFields as $field) {
            if (!isset($post[$field]) || $post[$field] === '') {
                apijson(1, [], "缺少必要参数: {$field}");
            }
        }
        $cleanJson = html_entity_decode($post['goods_list']);
        $goods_list = json_decode($cleanJson, true);
        // 开始事务
        Db::startTrans();
        try {
            // 生成订单号
            $orderNo = date("YmdHis") . mt_rand(1000, 9999);
            $orderDetails = [];
            $totalPrice = 0;
            $goodsPrice = 0;
            $expressPrice = 0;
            foreach ( $goods_list as $key=>$val ){
                // 计算商品总价和准备订单详情数据
                $goodsId = $val['goods_id'];
                $goodsNum = $val['goods_num'];

                // 查询商品信息
                $goods = $this->GoodsModel->where('id', $goodsId)->find();
                if (!$goods) {
                    Db::rollback();
                    apijson(1, [], "商品ID: {$goodsId} 不存在");
                }
                // 检查商品状态
                if ($goods['is_list'] != 1) {
                    Db::rollback();
                    apijson(1, [], '商品已下架');
                }

                $price = $goods['price'];
                $itemTotal = $price * $goodsNum;
                $goodsPrice += $itemTotal;

                $orderDetails[] = [
                    'goods_id' => $goodsId,
                    'num' => $goodsNum,
                    'price' => $price,
                    'name' => $goods['name'],
                    'cimage' => $goods['cimage'],
                    'total_price' => $itemTotal,
                    'freight' => $goods['freight'],
                    'weight' => $goods['weight'],
                    'remark' => $val['remark'] ?? '',
                    'specifications' => $val['specifications'] ?? '',
                ];


            }
                // 计算运费 (可根据实际需求调整)
                $expressPrice = $this->calculateExpressPrice($post,$goods);
                $totalPrice = $goodsPrice + $expressPrice;
                // 获取收货地址信息
                $userAddressInfo = $this->userAddressModel->where('id',$post['address_id'])->find();
                $addressArr = explode('/', $userAddressInfo['city']);


                // 创建订单主表数据
                $orderData = [
                    'merchant_id' => $post['merchant_id'],
                    'user_id' => $post['user_id'],
                    'order_no' => $orderNo,
                    'goods_price' => $goodsPrice,
                    'express_price' => $expressPrice,
                    'total_price' => $totalPrice,
                    'address_id' => $post['address_id'],
                    'accept_mobile' => $userAddressInfo['mobile'],
                    'accept_name' => $userAddressInfo['name'],
                    'accept_province' => $addressArr[0] ?? '',
                    'accept_city' => $addressArr[1] ?? '',
                    'accept_exp_area' => $addressArr[2] ?? '',
                    'accept_address' => $userAddressInfo['address'],
                    'remark' => $post['remark'] ?? '',
                    'specifications' => $post['specifications'] ?? '',
                    'is_pay' => 0, // 未支付
                    'pay_type' => 1, // 微信支付
                    'create_time' => time(),
                    'status' => 0, // 订单状态：待付款
                ];
                // 保存订单
                $result = $this->orderModel->allowField(true)->save($orderData);
                if (!$result) {
                    Db::rollback();
                    apijson(1, [], '创建订单失败');
                }

                // 获取新创建的订单ID
                $orderId = $this->orderModel->getLastInsID();

                // 保存订单详情
                foreach ($orderDetails as &$detail) {
                    $detail['order_id'] = $orderId;
                }
                $detailResult = $this->orderDetailModel->insertAll($orderDetails);
                if (!$detailResult) {
                    Db::rollback();
                    apijson(1, [], '创建订单详情失败');
                }
            // 提交事务
            Db::commit();

            // 查询创建的订单详情
            $order = $this->orderModel->with(['orderDetail'])->where('id', $orderId)->find();

            apijson(0, $order, '创建订单成功');

        } catch (UploadException $e) {
            // 回滚事务
            Db::rollback();
            apijson(1, [], '创建订单失败: ' . $e->getMessage());
        }

    }


}
