<?php

namespace app\model;

use think\Model;


class Collect extends Model
{

    // 表名
    protected $name = 'collect';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    public function works()
    {
        return $this->belongsTo('app\model\HairWorks', 'works_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

}
