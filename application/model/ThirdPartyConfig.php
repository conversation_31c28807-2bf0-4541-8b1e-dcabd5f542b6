<?php

namespace app\model;

use think\Model;

class ThirdPartyConfig extends Model
{
    // 表名
    protected $name = 'merchant_third_party_config';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    // 追加属性
    protected $append = [
        'platform_text'
    ];

    public function getPlatformTextAttr($value, $data)
    {
        $platform = $data['platform'] ?? '';
        $list = [
            'douyin' => '抖音',
            'meituan' => '美团'
        ];
        return $list[$platform] ?? $platform;
    }

    // 关联商户
    public function merchant()
    {
        return $this->belongsTo('app\model\Merchant', 'merchant_id', 'id');
    }
}