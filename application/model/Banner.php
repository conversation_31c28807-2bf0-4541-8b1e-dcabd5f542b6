<?php

namespace app\model;

use think\Model;


class Banner extends Model
{





    // 表名
    protected $name = 'banner';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'is_reservation_list_text',
        'reservation_time_text',
        'position_text',
        'create_time_text',
        'update_time_text'
    ];



    public function getIsReservationListList()
    {
        return ['Yes' => __('Is_reservation_list Yes'), 'No' => __('Is_reservation_list No')];
    }

    public function getPositionList()
    {
        return ['home_page' => __('Position home_page'), 'personal_center' => __('Position personal_center')];
    }


    public function getIsReservationListTextAttr($value, $data)
    {
        $value = $value ?: ($data['is_reservation_list'] ?? '');
        $list = $this->getIsReservationListList();
        return $list[$value] ?? '';
    }


    public function getReservationTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['reservation_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getPositionTextAttr($value, $data)
    {
        $value = $value ?: ($data['position'] ?? '');
        $list = $this->getPositionList();
        return $list[$value] ?? '';
    }


    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['create_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUpdateTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['update_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setReservationTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUpdateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function merchant()
    {
        return $this->belongsTo('Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function manage()
    {
        return $this->belongsTo('app\model\store\Manage', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
