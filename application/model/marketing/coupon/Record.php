<?php

namespace app\model\marketing\coupon;

use think\Model;


class Record extends Model
{

    // 表名
    protected $name = 'marketing_record';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    //关联优惠券
    public function coupon()
    {
        return $this->belongsTo('app\model\marketing\coupon\Coupon', 'coupon_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
