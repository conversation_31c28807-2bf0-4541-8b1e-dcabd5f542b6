<?php

namespace app\model;

use think\Model;


class Identity extends Model
{
    // 表名
    protected $name = 'user_identity';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;



    public function merchant()
    {
        return $this->belongsTo('app\model\Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function manage()
    {
        return $this->belongsTo('app\model\store\Manage', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
