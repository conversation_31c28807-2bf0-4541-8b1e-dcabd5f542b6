<?php

namespace app\model\hair;

use think\Model;

class Record extends Model
{
    // 表名
    protected $name = 'hair_record';
    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'create_time_text'
    ];
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }
    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['create_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function merchant()
    {
        return $this->belongsTo('app\model\Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function manage()
    {
        return $this->belongsTo('app\model\store\Manage', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('app\merchant\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    public function appeal()
    {
        return $this->belongsTo('app\model\Appeal', 'id', 'record_id', [], 'LEFT')->setEagerlyType(0);
    }
}
