<?php
namespace app\model;

use think\Model;

class Label extends Model
{
    // 表名
    protected $name = 'label';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;
    // 追加属性
    protected $append = [
        'create_time_text'
    ];
    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['create_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    public function merchant()
    {
        return $this->belongsTo('app\model\Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

}

