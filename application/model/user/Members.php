<?php



namespace app\model\user;



use think\Model;





class Members extends Model

{



    



    



    // 表名

    protected $name = 'user_members';

    

    // 自动写入时间戳字段

    protected $autoWriteTimestamp = false;



    // 定义时间戳字段名

    protected $createTime = false;

    protected $updateTime = false;

    protected $deleteTime = false;



    // 追加属性

    protected $append = [



    ];

    



    















    public function merchant()

    {

        return $this->belongsTo('app\admin\model\Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);

    }

}

