<?php

namespace app\model;

use think\Model;


class Scheduling extends Model
{





    // 表名
    protected $name = 'scheduling';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'work_time_text',
        'create_time_text'
    ];






    public function getWorkTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['work_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d", $value) : $value;
    }


    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['create_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setWorkTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function merchant()
    {
        return $this->belongsTo('Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function manage()
    {
        return $this->belongsTo('app\model\store\Manage', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('app\store\model\User', 'hair_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
