<?php
namespace addons\BsPaySdk\config;

class MerConfig {

    /**
     * @var string 商户rsa私钥，用来进行对斗拱接口调用的请求数据加签
     */
    public $rsa_merch_private_key = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCPBTYFy4c+Rni8qqN9pVcOoEVs3HvdAZLeFsx1TiM2lXNbhL/gBJKzzsrbQGpAzxboj9b0JSrPL3MYxVcLKeYapdVwgNuflkJpxCAIEOyjN/oeRFhEEcDum++MocHHgr0oPzPewXPc4u7gRZrLTB4r4lscBtZcPcwkhI7PzNtSvVY4TwjrTLM4jbJ/3rg6jjIXsJp2AVMoe0lJ3u7mitvJH6A5vRwJ8W+mXuQNTVmKF3DWDCdVhvxPfXLsJjT0PddAElsm7EykXKtl6vnwboXC/LvWnM82t2xf1O1f9H629OwX1aA9/dePTYqumwn9S03UpsWBLFPso6mB4XQ+LOMfAgMBAAECggEAEDaZhLepecxYZJ79kMFdKKnPpQMrwYarpSS1gfjdMhNoM/BNJjKuhNbFFWzsfrNbV/ryddP+0eUBqcW5U+kojk3NcAcTnrhUiBP1Q6TThr9SSfsafID8u7AOzIUL6DewhO8BTNfmcByD0wXhAfn7k6MCDB/UTjmtOliSrgj9thfYWd13EZ2zcoBsl1uPbq+PvctedPoGRa2R+MQrCY2o2N+bTo6FMgijRnERGLOHz7Ogd5aFDuOwkynHZ1Z3Wi/bu0BX7t6NPQb1zbhFfjDfZrsQhuiDsqJ2WN/2XEYJG7BXajC+I9wgYSfjPq+QMOFZ3qtX+FVdOKP0CPTfyy9Q0QKBgQC7/Eh1o6MEVmdMa9u8JPq48+p5iSWxLHYIX2sa61HumtrwynPMAZ0aPVQnZqkEPu6lvGEeWvtg94708/Odj+82fbdJc5lZr/ZlTVmaVWj+7V4WZQX7q20WoI3W0pEaOlzbcFAsRGjOXse2448nrHCc6Zm0W9+tiKNc3OSksJN9LwKBgQDCxCNZcNAMkM0H3MWAxUYRSmWNtkdAV8kaczDvpJKoE1vaAED/71G4sjM5DK7BjNqsScDtd7y1qBn1IsYl3gC5FQzuVwxO2XJ11Iv00up1Fd3MKEesp2tPp2Nej03gyI6ETxk+9X1j1jfn3KkiRTk3RCZsNcaTMqKaqKSkr7DdEQKBgQCLHGy6sXeHVDFpOkAHgQ4RJF+VxMohzJckJwhpiU6+VdZDbm42v9L0ggHujmfw/mk4OKld/1IbNdeSqZzUMpmBJ5MlRehrmG2sFYuDTEVAsYN46xV8YiV+Ix4TpQs/74Sb1wddWtp8LWN5kfh0EYXceHoIIBzsnDrYE5cX0Ju0fQKBgCF/qrTPenkuzpTzm+SyCYMf0Ke3Gjou3x/1gA03uSG/cqTkNknO17d0ruq5C7WujE3lwZEWbxavewd2m64XqPgsbrTvs+Y78Dvj+s55GGpbAHME2o8PnJ1upOC2tSZcWGbZU/ZIMvDg/2yZv11tmpGu3wztkuHua5k+OFM7BKiBAoGBAI6USnLbvQVFKMesMV0PxrKg25nluLlsQ9DEOOAAiOwxthfZPGW2sq93hCqP05PB377z/pRezb+veu01GChPAk8B2BfLaCnGF96b6NtsTToJk/3tal7i9aqmEENMv1xAyOspLUqKayouOg3R849ePQo6YjO/0ikkJkBcrf8GA66Y";

    /**
     * @var string 商户汇付rsa公钥，用来进行对斗拱接口应答数据的验签，以及部分请求数据非对称加密
     */
	public $rsa_huifu_public_key = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyXPB4k0na7mlpeaWVQsNKpoTZc6JRVbGGCaRJblZl9d0pyA9GI9V/w6jcOzMqgr+lutBrrswcuud07W+AvxSHXZl5C+2enp41e1mZm1sLr/7KILlEI0aZmUJvTwOSDNv4wNbquy8F9At53wfKURkKe57/T+ovnzVdp8+QbdufGaR4Cca/odpShJNc9g3LdicijySlesC+rUJgWehkP3F/LzpeHq4j5qE2BkazlkSpFtRGsfLVqeMRLIF8WrgIgDsrU8jB2xjl6+IqDZoJOGlmox82F5Cb8ugl9avUxBB0MOA6kB+Gd6QBbPLZ32OmAaTyeUNwbkP8IZyEELc34zbiQIDAQAB';
	
	public $product_id = 'PAYUN';
	
//	public $sys_id = '6666000167527479';
	public $sys_id = '6666000166784174';

    public $huifu_id = '6666000167527479';
}