define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'project/index' + location.search,
                    add_url: 'project/add',
                    edit_url: 'project/edit',
                    del_url: 'project/del',
                    multi_url: 'project/multi',
                    import_url: 'project/import',
                    table: 'project',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'merchant.merchant_name', title: __('Merchant.merchant_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'manage.name', title: __('Manage.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'hair.nickname', title: __('发型师'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'type', title: __('项目类型'), operate:'BETWEEN'},
                        {field: 'money', title: __('Money'), operate:'BETWEEN'},
                        {field: 'activity_money', title: __('活动价'), operate:'BETWEEN'},
                        {field: 'service_duration', title: __('Service_duration'), operate: 'LIKE'},
                        {field: 'is_card', title: __('Is_card'), searchList: {"1":__('Is_card 1'),"2":__('Is_card 2')}, formatter: Table.api.formatter.status},
                        {field: 'create_time', title: __('Create_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                $(document).ready(function() {
                    $(document).on("change", "#c-merchant_id", function () {
                        var merchant_id = $(this).val();
                        if (!merchant_id) {
                            return;
                        }

                        // 清空并禁用项目类型下拉列表
                        $("#c-type").empty().prop('disabled', true);

                        // 显示加载提示
                        Backend.api.toastr.info('正在加载项目类型数据...');

                        // 发送AJAX请求获取项目类型列表
                        $.ajax({
                            url: "project/getTypeList",
                            type: 'post',
                            dataType: 'json',
                            data: {merchant_id: merchant_id},
                            success: function (ret) {
                                console.log(ret)
                                if (ret.code == 200) {
                                    var html = '';
                                    // 添加默认选项
                                    html += '<option value="">请选择项目类型</option>';

                                    // 添加项目类型选项
                                    if (ret.data && ret.data.length > 0) {
                                        for (var i = 0; i < ret.data.length; i++) {
                                            html += '<option value="' + ret.data[i].name + '">' + ret.data[i].name + '</option>';
                                        }
                                    }

                                    // 更新项目类型下拉列表
                                    $("#c-type").html(html).prop('disabled', false);

                                    // 刷新下拉列表
                                    $("#c-type").selectpicker('refresh');

                                    Backend.api.toastr.success('项目类型数据加载成功');
                                } else {
                                    Backend.api.toastr.error(ret.msg || '项目类型数据加载失败');
                                    // 启用项目类型下拉列表
                                    $("#c-type").prop('disabled', false);
                                }
                            },
                            error: function (e) {
                                Backend.api.toastr.error('项目类型数据加载失败: ' + e.message);
                                // 启用项目类型下拉列表
                                $("#c-type").prop('disabled', false);
                            }
                        });

                        // 同时也加载门店列表
                        // // 清空并禁用门店下拉列表
                        // $("#c-store_id").empty().prop('disabled', true);
                        //
                        // // 发送AJAX请求获取门店列表
                        // $.ajax({
                        //     url: "type/getStoreList",
                        //     type: 'post',
                        //     dataType: 'json',
                        //     data: {merchant_id: merchant_id},
                        //     success: function (ret) {
                        //         if (ret.code == 200) {
                        //             var html = '';
                        //             // 添加默认选项
                        //             html += '<option value="">请选择门店</option>';
                        //
                        //             // 添加门店选项
                        //             if (ret.data && ret.data.length > 0) {
                        //                 for (var i = 0; i < ret.data.length; i++) {
                        //                     html += '<option value="' + ret.data[i].id + '">' + ret.data[i].name + '</option>';
                        //                 }
                        //             }
                        //
                        //             // 更新门店下拉列表
                        //             $("#c-store_id").html(html).prop('disabled', false);
                        //
                        //             // 刷新下拉列表
                        //             $("#c-store_id").selectpicker('refresh');
                        //         } else {
                        //             Backend.api.toastr.error(ret.msg || '门店数据加载失败');
                        //             // 启用门店下拉列表
                        //             $("#c-store_id").prop('disabled', false);
                        //         }
                        //     },
                        //     error: function (e) {
                        //         Backend.api.toastr.error('门店数据加载失败: ' + e.message);
                        //         // 启用门店下拉列表
                        //         $("#c-store_id").prop('disabled', false);
                        //     }
                        // });
                    });
                });
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
