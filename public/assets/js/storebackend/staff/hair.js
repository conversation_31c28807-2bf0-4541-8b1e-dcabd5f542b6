define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'staff/hair/index' + location.search,
                    add_url: 'staff/hair/add',
                    edit_url: 'staff/hair/edit',
                    del_url: 'staff/hair/del',
                    // verify_url:'staff/hair/verify',
                    multi_url: 'staff/hair/multi',
                    // import_url: 'verify/hair/import',
                    table: 'verify_hair',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'merchant.merchant_name', title: __('Merchant.merchant_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'manage.name', title: __('Manage.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'birthday', title: __('Birthday'), operate: 'LIKE'},
                        {field: 'haircut_duration', title: __('Haircut_duration'), operate: 'LIKE'},
                        {field: 'level.name', title: __('Level'), operate: 'LIKE'},
                        {field: 'badabababa', title: __('Badabababa'), operate:'BETWEEN'},
                        {field: 'intention_city', title: __('Intention_city'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'is_training', title: __('Is_training'), searchList: {"1":__('Is_training 1'),"2":__('Is_training 2')}, formatter: Table.api.formatter.normal},
                        {field: 'is_verify', title: __('Is_verify'), searchList: {"1":__('Is_verify 1'),"2":__('Is_verify 2'),"3":__('Is_verify 3')}, formatter: Table.api.formatter.normal},
                        {field: 'switch', title: __('启用'), searchList: {"1":__('Yes'),"2":__('No')}, table: table, formatter: Table.api.formatter.toggle},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
