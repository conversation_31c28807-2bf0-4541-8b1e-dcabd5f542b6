define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'appeal/index' + location.search,
                    // add_url: 'appeal/add',
                    // edit_url: 'appeal/edit',
                    // del_url: 'appeal/del',
                    verify_url: 'appeal/verify',
                    multi_url: 'appeal/multi',
                    import_url: 'appeal/import',
                    table: 'appeal',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'appeal_image', title: __('Appeal_image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'is_verify', title: __('Is_verify'), searchList: {"1":__('Is_verify 1'),"2":__('Is_verify 2')}, formatter: Table.api.formatter.normal},
                        {field: 'appeal_time', title: __('Appeal_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'verify_time', title: __('Verify_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'merchant.merchant_name', title: __('Merchant.merchant_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'manage.name', title: __('Manage.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'user.nickname', title: __('User.nickname'), operate: 'LIKE'},
                        {
                            field: 'operate', title: __('Operate'), table: table,
                            events: Table.api.events.operate,
                            formatter: Table.api.formatter.operate,
                            buttons: [
                                {
                                    name: 'verify',
                                    text:'审核',
                                    title: '审核',
                                    icon: 'fa fa-check',
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    url: 'appeal/verify',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }

                                },
                            ]
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        verify: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
