define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'evaluate.index/index' + location.search,
                    // add_url: 'evaluate/add',
                    // edit_url: 'evaluate/edit',
                    // del_url: 'evaluate/del',
                    reply_url: 'evaluate.index/reply',
                    multi_url: 'evaluate.index/multi',
                    import_url: 'evaluate.index/import',
                    table: 'evaluate',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'merchant.merchant_name', title: __('Merchant.merchant_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'manage.name', title: __('Manage.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'user.nickname', title: __('User.nickname'), operate: 'LIKE'},
                        {field: 'content', title: __('Content'), operate: 'LIKE'},
                        {field: 'vote_1', title: __('Vote_1')},
                        {field: 'vote_2', title: __('Vote_2')},
                        {field: 'vote_3', title: __('Vote_3')},
                        {field: 'vote_4', title: __('Vote_4')},
                        {field: 'vote_5', title: __('Vote_5')},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"2":__('Status 2')}, formatter: Table.api.formatter.toggle},
                        {field: 'create_time', title: __('Create_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'is_type', title: __('Is_type'), searchList: {"1":__('Is_type 1'),"2":__('Is_type 2')}, formatter: Table.api.formatter.normal},
                        {field: 'is_del', title: __('Is_del'), searchList: {"1":__('Is_del 1'),"2":__('Is_del 2')}, formatter: Table.api.formatter.normal},
                        {
                            field: 'operate', title: __('Operate'), table: table,
                            events: Table.api.events.operate,
                            formatter: Table.api.formatter.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    title: '详情',
                                    icon: 'fa fa-navicon',
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    url: 'evaluate/detail',
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }

                                },
                                {
                                    name: 'reply',
                                    title: '回复',
                                    icon: 'fa fa-envelope-o',
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    url: 'evaluate/reply',
                                    hidden:function(row){
                                        if( row.is_reply == 1){
                                            return true
                                        }else {
                                            return false
                                        }
                                    },
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }

                                },
                            ]
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        detail: function () {
            Controller.api.bindevent();
        },
        reply: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
