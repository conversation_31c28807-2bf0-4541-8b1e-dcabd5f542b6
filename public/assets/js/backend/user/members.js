define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {



    var Controller = {

        index: function () {

            // 初始化表格参数配置

            Table.api.init({

                extend: {

                    index_url: 'user/members/index' + location.search,

                    add_url: 'user/members/add',

                    edit_url: 'user/members/edit',

                    del_url: 'user/members/del',

                    multi_url: 'user/members/multi',

                    import_url: 'user/members/import',

                    table: 'user_members',

                }

            });



            var table = $("#table");



            // 初始化表格

            table.bootstrapTable({

                url: $.fn.bootstrapTable.defaults.extend.index_url,

                pk: 'id',

                sortName: 'id',

                columns: [

                    [

                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'title', title: __('Title'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'money', title: __('Money'), operate:'BETWEEN'},
                        {field: 'effective_days', title: __('Effective_days')},
                        {field: 'first_order', title: __('First_order')},
                        {field: 'merchant.merchant_name', title: __('Merchant.merchant_name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}

                    ]

                ]

            });



            // 为表格绑定事件

            Table.api.bindevent(table);

        },

        add: function () {

            Controller.api.bindevent();

        },

        edit: function () {

            Controller.api.bindevent();

        },

        api: {

            bindevent: function () {

                Form.api.bindevent($("form[role=form]"));

            }

        }

    };

    return Controller;

});

